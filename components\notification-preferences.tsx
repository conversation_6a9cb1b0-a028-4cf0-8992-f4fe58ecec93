"use client"

import { useState, useEffect } from "react"
import { useNotifications } from "@/lib/hooks/use-notifications"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Bell, 
  BellRing, 
  BellOff, 
  Smartphone, 
  Monitor, 
  Clock, 
  Settings,
  Check,
  X,
  Info
} from "lucide-react"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

interface NotificationPreferencesProps {
  className?: string
}

export function NotificationPreferences({ className }: NotificationPreferencesProps) {
  const { 
    preferences, 
    updatePreferences, 
    initializePushNotifications,
    loading 
  } = useNotifications()
  
  const [saving, setSaving] = useState(false)
  const [pushSupported, setPushSupported] = useState(false)
  const [pushPermission, setPushPermission] = useState<NotificationPermission>("default")

  // Vérifier le support des notifications push
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setPushSupported('Notification' in window && 'serviceWorker' in navigator)
      setPushPermission(Notification.permission)
    }
  }, [])

  const handleToggle = async (key: string, value: boolean) => {
    if (!preferences) return

    try {
      setSaving(true)
      
      if (key === 'pushEnabled' && value && pushPermission !== 'granted') {
        // Demander la permission et initialiser FCM
        const success = await initializePushNotifications()
        if (!success) {
          toast.error("Impossible d'activer les notifications push")
          return
        }
        setPushPermission('granted')
      }

      const updates = { [key]: value }
      await updatePreferences(updates)
      toast.success("Préférences mises à jour")
    } catch (error) {
      console.error("Erreur lors de la mise à jour:", error)
      toast.error("Impossible de mettre à jour les préférences")
    } finally {
      setSaving(false)
    }
  }

  const handleCategoryToggle = async (category: string, value: boolean) => {
    if (!preferences) return

    try {
      setSaving(true)
      const updates = {
        categories: {
          ...preferences.categories,
          [category]: value
        }
      }
      await updatePreferences(updates)
      toast.success("Préférences mises à jour")
    } catch (error) {
      console.error("Erreur lors de la mise à jour:", error)
      toast.error("Impossible de mettre à jour les préférences")
    } finally {
      setSaving(false)
    }
  }

  const handleQuietHoursToggle = async (enabled: boolean) => {
    if (!preferences) return

    try {
      setSaving(true)
      const updates = {
        quietHours: {
          ...preferences.quietHours,
          enabled
        }
      }
      await updatePreferences(updates)
      toast.success("Heures silencieuses mises à jour")
    } catch (error) {
      console.error("Erreur lors de la mise à jour:", error)
      toast.error("Impossible de mettre à jour les heures silencieuses")
    } finally {
      setSaving(false)
    }
  }

  const handleQuietHoursChange = async (field: 'start' | 'end', value: string) => {
    if (!preferences) return

    try {
      setSaving(true)
      const updates = {
        quietHours: {
          ...preferences.quietHours,
          [field]: value
        }
      }
      await updatePreferences(updates)
    } catch (error) {
      console.error("Erreur lors de la mise à jour:", error)
      toast.error("Impossible de mettre à jour les heures silencieuses")
    } finally {
      setSaving(false)
    }
  }

  const getPermissionBadge = () => {
    switch (pushPermission) {
      case 'granted':
        return <Badge variant="default" className="bg-green-500"><Check className="h-3 w-3 mr-1" />Autorisées</Badge>
      case 'denied':
        return <Badge variant="destructive"><X className="h-3 w-3 mr-1" />Refusées</Badge>
      default:
        return <Badge variant="outline"><Info className="h-3 w-3 mr-1" />En attente</Badge>
    }
  }

  if (loading || !preferences) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Préférences de notification
          </CardTitle>
          <CardDescription>
            Chargement de vos préférences...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="h-4 w-32 bg-muted rounded animate-pulse" />
                  <div className="h-3 w-48 bg-muted rounded animate-pulse" />
                </div>
                <div className="h-6 w-11 bg-muted rounded-full animate-pulse" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Préférences de notification
        </CardTitle>
        <CardDescription>
          Gérez comment vous souhaitez recevoir les notifications
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Statut des notifications push */}
        {pushSupported && (
          <Alert>
            <Bell className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>Statut des notifications push: {getPermissionBadge()}</span>
              {pushPermission === 'denied' && (
                <span className="text-sm text-muted-foreground">
                  Vous devez autoriser les notifications dans les paramètres de votre navigateur
                </span>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Paramètres généraux */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Paramètres généraux</h3>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                Notifications push
              </Label>
              <p className="text-sm text-muted-foreground">
                Recevoir des notifications sur votre appareil même quand l'app est fermée
              </p>
            </div>
            <Switch
              checked={preferences.pushEnabled}
              onCheckedChange={(checked) => handleToggle('pushEnabled', checked)}
              disabled={saving || !pushSupported}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Monitor className="h-4 w-4" />
                Notifications in-app
              </Label>
              <p className="text-sm text-muted-foreground">
                Afficher les notifications dans l'application
              </p>
            </div>
            <Switch
              checked={preferences.inAppEnabled}
              onCheckedChange={(checked) => handleToggle('inAppEnabled', checked)}
              disabled={saving}
            />
          </div>
        </div>

        <Separator />

        {/* Catégories de notifications */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Types de notifications</h3>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Notifications générales</Label>
                <p className="text-sm text-muted-foreground">
                  Annonces et informations générales
                </p>
              </div>
              <Switch
                checked={preferences.categories.general}
                onCheckedChange={(checked) => handleCategoryToggle('general', checked)}
                disabled={saving}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Actualités</Label>
                <p className="text-sm text-muted-foreground">
                  Nouvelles actualités et articles
                </p>
              </div>
              <Switch
                checked={preferences.categories.news}
                onCheckedChange={(checked) => handleCategoryToggle('news', checked)}
                disabled={saving}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Pages</Label>
                <p className="text-sm text-muted-foreground">
                  Nouvelles pages et mises à jour de contenu
                </p>
              </div>
              <Switch
                checked={preferences.categories.pages}
                onCheckedChange={(checked) => handleCategoryToggle('pages', checked)}
                disabled={saving}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Notifications système</Label>
                <p className="text-sm text-muted-foreground">
                  Mises à jour importantes et notifications de sécurité
                </p>
              </div>
              <Switch
                checked={preferences.categories.system}
                onCheckedChange={(checked) => handleCategoryToggle('system', checked)}
                disabled={saving}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Heures silencieuses */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Heures silencieuses
          </h3>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Activer les heures silencieuses</Label>
              <p className="text-sm text-muted-foreground">
                Ne pas recevoir de notifications pendant certaines heures
              </p>
            </div>
            <Switch
              checked={preferences.quietHours?.enabled || false}
              onCheckedChange={handleQuietHoursToggle}
              disabled={saving}
            />
          </div>

          {preferences.quietHours?.enabled && (
            <div className="grid grid-cols-2 gap-4 pl-4 border-l-2 border-muted">
              <div className="space-y-2">
                <Label htmlFor="quiet-start">Début</Label>
                <Input
                  id="quiet-start"
                  type="time"
                  value={preferences.quietHours.start}
                  onChange={(e) => handleQuietHoursChange('start', e.target.value)}
                  disabled={saving}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="quiet-end">Fin</Label>
                <Input
                  id="quiet-end"
                  type="time"
                  value={preferences.quietHours.end}
                  onChange={(e) => handleQuietHoursChange('end', e.target.value)}
                  disabled={saving}
                />
              </div>
            </div>
          )}
        </div>

        {/* Informations supplémentaires */}
        {!pushSupported && (
          <Alert>
            <BellOff className="h-4 w-4" />
            <AlertDescription>
              Les notifications push ne sont pas supportées par votre navigateur ou appareil.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
