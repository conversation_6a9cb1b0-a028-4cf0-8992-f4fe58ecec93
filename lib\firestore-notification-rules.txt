// Règles Firestore pour le système de notifications
// Ajoutez ces règles à votre fichier firestore.rules existant
// IMPORTANT: Ajoutez ces règles AVANT la règle par défaut match /{document=**}

    // === RÈGLES POUR LE SYSTÈME DE NOTIFICATIONS ===

    // Collection des notifications - Admins seulement pour créer/modifier/supprimer
    match /notifications/{notificationId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin() || hasAnyRole(['admin']);
    }

    // Notifications utilisateur - Les utilisateurs peuvent lire/modifier les leurs, admins peuvent tout gérer
    match /userNotifications/{userNotificationId} {
      allow read, update: if isAuthenticated() &&
                          (resource.data.userId == request.auth.uid || isAdmin() || hasAnyRole(['admin']));
      allow create: if isAuthenticated() &&
                   (request.resource.data.userId == request.auth.uid || isAdmin() || hasAnyRole(['admin']));
      allow delete: if isAdmin() || hasAnyRole(['admin']);
    }

    // Préférences de notification - Les utilisateurs gèrent les leurs
    match /notificationPreferences/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || hasAnyRole(['admin']);
    }

    // Tokens FCM des utilisateurs - Les utilisateurs gèrent les leurs, admins peuvent lire tous
    match /userTokens/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || hasAnyRole(['admin']);
    }

    // Analytiques de notifications (optionnel) - Admins seulement
    match /notificationAnalytics/{analyticsId} {
      allow read, write: if isAdmin() || hasAnyRole(['admin']);
    }

    // Modèles de notifications (optionnel) - Admins seulement
    match /notificationTemplates/{templateId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin() || hasAnyRole(['admin']);
    }

    // Notifications collection - Admin only for create/update/delete
    match /notifications/{notificationId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin() || hasAdminRole() || hasPermission('admin');
    }

    // User notifications - Users can read their own, admins can manage all
    match /userNotifications/{userNotificationId} {
      allow read, update: if isAuthenticated() &&
                          (resource.data.userId == request.auth.uid || isAdmin() || hasAdminRole());
      allow create: if isAuthenticated() &&
                   (request.resource.data.userId == request.auth.uid || isAdmin() || hasAdminRole());
      allow delete: if isAdmin() || hasAdminRole();
    }

    // Notification preferences - Users can manage their own
    match /notificationPreferences/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || hasAdminRole();
    }

    // User FCM tokens - Users can manage their own, admins can read all
    match /userTokens/{userId} {
      allow read, write: if isOwner(userId) || isAdmin() || hasAdminRole();
    }

    // Notification analytics (optional) - Admin only
    match /notificationAnalytics/{analyticsId} {
      allow read, write: if isAdmin() || hasAdminRole();
    }

    // Notification templates (optional) - Admin only
    match /notificationTemplates/{templateId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin() || hasAdminRole();
    }
  }
}

// Additional rules for existing collections that might be referenced in notifications

// News collection - for linking notifications to news articles
match /news/{newsId} {
  allow read: if isAuthenticated() &&
              (resource.data.isPublished == true || isAdmin() || hasAdminRole()) &&
              (resource.data.targetGroups.hasAny(['all']) ||
               resource.data.targetGroups.hasAny(
                 get(/databases/$(database)/documents/users/$(request.auth.uid)).data.groups
               ));
  allow create, update, delete: if isAdmin() || hasAdminRole() ||
                                hasPermission('create:news') ||
                                hasPermission('update:news') ||
                                hasPermission('delete:news');
}

// Pages collection - for linking notifications to pages
match /menuItems/{pageId} {
  allow read: if isAuthenticated() &&
              (resource.data.isPublished == true || isAdmin() || hasAdminRole()) &&
              (resource.data.targetGroups.hasAny(['all']) ||
               resource.data.targetGroups.hasAny(
                 get(/databases/$(database)/documents/users/$(request.auth.uid)).data.groups
               ));
  allow create, update, delete: if isAdmin() || hasAdminRole() ||
                                hasPermission('create:pages') ||
                                hasPermission('update:pages') ||
                                hasPermission('delete:pages');
}

// Users collection - for targeting notifications
match /users/{userId} {
  allow read: if isAuthenticated() &&
              (request.auth.uid == userId || isAdmin() || hasAdminRole() ||
               hasPermission('read:users'));
  allow update: if isAuthenticated() &&
                (request.auth.uid == userId || isAdmin() || hasAdminRole() ||
                 hasPermission('update:users'));
  allow create, delete: if isAdmin() || hasAdminRole() ||
                        hasPermission('create:users') ||
                        hasPermission('delete:users');
}

// Groups collection - for targeting notifications by groups
match /groups/{groupId} {
  allow read: if isAuthenticated();
  allow create, update, delete: if isAdmin() || hasAdminRole() ||
                                hasPermission('create:groups') ||
                                hasPermission('update:groups') ||
                                hasPermission('delete:groups');
}

// Example compound queries that might be used:
// - Get all notifications for a user's groups
// - Get unread notifications for a user
// - Get notifications by type and target
// - Get notification analytics by date range

// Security considerations:
// 1. All notification operations require authentication
// 2. Users can only access their own notification data
// 3. Admins have full access to all notification data
// 4. Notification creation is restricted to admins
// 5. User preferences are private to each user
// 6. FCM tokens are protected and only accessible by the owner
// 7. Content linking respects existing content permissions
// 8. Group-based targeting respects user group memberships
