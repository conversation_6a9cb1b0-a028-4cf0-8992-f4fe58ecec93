"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  Send, 
  BarChart3, 
  Users, 
  Bell, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Database
} from "lucide-react"
import { NotificationAnalyticsService, type AnalyticsSummary, type AnalyticsSettings } from "@/lib/notification-analytics"
import { toast } from "sonner"

interface NotificationAnalyticsProps {
  className?: string
}

export function NotificationAnalytics({ className }: NotificationAnalyticsProps) {
  const [summary, setSummary] = useState<AnalyticsSummary | null>(null)
  const [settings, setSettings] = useState<AnalyticsSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [showSettings, setShowSettings] = useState(false)

  // États pour les paramètres
  const [analyticsEnabled, setAnalyticsEnabled] = useState(true)
  const [dailyLimit, setDailyLimit] = useState("45000")
  const [autoDisable, setAutoDisable] = useState(true)
  const [priorityMode, setPriorityMode] = useState<'essential' | 'normal' | 'full'>('normal')

  // Charger les données
  const loadData = async () => {
    try {
      setLoading(true)
      
      // Charger les paramètres
      const analyticsSettings = await NotificationAnalyticsService.getAnalyticsSettings()
      setSettings(analyticsSettings)
      setAnalyticsEnabled(analyticsSettings.enabled)
      setDailyLimit(analyticsSettings.dailyLimit.toString())
      setAutoDisable(analyticsSettings.autoDisableOnLimit)
      setPriorityMode(analyticsSettings.priorityMode)

      // Charger le résumé seulement si les analytics sont activées
      if (analyticsSettings.enabled) {
        const analyticsSummary = await NotificationAnalyticsService.getAnalyticsSummary(30)
        setSummary(analyticsSummary)
      } else {
        setSummary(null)
      }
    } catch (error) {
      console.error("Erreur lors du chargement des analytics:", error)
      toast.error("Impossible de charger les analytics")
    } finally {
      setLoading(false)
    }
  }

  // Sauvegarder les paramètres
  const saveSettings = async () => {
    try {
      setUpdating(true)
      
      const newSettings: Partial<AnalyticsSettings> = {
        enabled: analyticsEnabled,
        dailyLimit: parseInt(dailyLimit),
        autoDisableOnLimit: autoDisable,
        priorityMode: priorityMode
      }

      await NotificationAnalyticsService.updateAnalyticsSettings(newSettings)
      toast.success("Paramètres sauvegardés avec succès")
      
      // Recharger les données
      await loadData()
    } catch (error) {
      console.error("Erreur lors de la sauvegarde:", error)
      toast.error("Impossible de sauvegarder les paramètres")
    } finally {
      setUpdating(false)
    }
  }

  // Réinitialiser l'usage quotidien
  const resetDailyUsage = async () => {
    try {
      await NotificationAnalyticsService.updateAnalyticsSettings({
        currentUsage: 0,
        lastReset: new Date().toISOString().split('T')[0],
        enabled: true
      })
      toast.success("Usage quotidien réinitialisé")
      await loadData()
    } catch (error) {
      console.error("Erreur lors de la réinitialisation:", error)
      toast.error("Impossible de réinitialiser l'usage")
    }
  }

  useEffect(() => {
    loadData()
  }, [])

  // Calculer le pourcentage d'usage
  const usagePercentage = settings ? (settings.currentUsage / settings.dailyLimit) * 100 : 0
  const isNearLimit = usagePercentage > 80
  const isAtLimit = usagePercentage >= 100

  // Déterminer la tendance
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />
      default: return <Minus className="h-4 w-4 text-gray-600" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Contrôles et statut Firebase */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="text-lg">Contrôle des Analytics</CardTitle>
            <CardDescription>
              Gestion de l'usage Firebase et des analytics de notifications
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4 mr-2" />
              Paramètres
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadData}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Actualiser
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Statut des analytics */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {settings?.enabled ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              <span className="font-medium">
                Analytics {settings?.enabled ? 'activées' : 'désactivées'}
              </span>
            </div>
            <Badge variant={settings?.enabled ? "default" : "destructive"}>
              {settings?.enabled ? 'Actif' : 'Inactif'}
            </Badge>
          </div>

          {/* Usage Firebase */}
          {settings && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Usage Firebase quotidien</Label>
                <span className="text-sm text-muted-foreground">
                  {settings.currentUsage.toLocaleString()} / {settings.dailyLimit.toLocaleString()} appels
                </span>
              </div>
              <Progress 
                value={usagePercentage} 
                className={`h-2 ${isAtLimit ? 'bg-red-100' : isNearLimit ? 'bg-yellow-100' : 'bg-green-100'}`}
              />
              {isAtLimit && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Limite quotidienne atteinte. Les analytics sont désactivées.
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={resetDailyUsage}
                      className="ml-2"
                    >
                      Réinitialiser
                    </Button>
                  </AlertDescription>
                </Alert>
              )}
              {isNearLimit && !isAtLimit && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Attention: Vous approchez de la limite quotidienne Firebase.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Paramètres détaillés */}
          {showSettings && (
            <div className="space-y-4 pt-4 border-t">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="analytics-enabled">Analytics activées</Label>
                  <Switch
                    id="analytics-enabled"
                    checked={analyticsEnabled}
                    onCheckedChange={setAnalyticsEnabled}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-disable">Désactivation automatique</Label>
                  <Switch
                    id="auto-disable"
                    checked={autoDisable}
                    onCheckedChange={setAutoDisable}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="daily-limit">Limite quotidienne Firebase</Label>
                <Input
                  id="daily-limit"
                  type="number"
                  value={dailyLimit}
                  onChange={(e) => setDailyLimit(e.target.value)}
                  min="1000"
                  max="50000"
                />
                <p className="text-sm text-muted-foreground">
                  Nombre maximum d'appels Firebase par jour (recommandé: 45000)
                </p>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowSettings(false)}
                >
                  Annuler
                </Button>
                <Button
                  onClick={saveSettings}
                  disabled={updating}
                >
                  {updating && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
                  Sauvegarder
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Statistiques principales */}
      {summary && settings?.enabled ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Notifications envoyées</CardTitle>
              <Send className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalSent.toLocaleString()}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {getTrendIcon(summary.recentTrend)}
                <span className="ml-1">30 derniers jours</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Taux d'ouverture</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.readRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                {summary.totalRead.toLocaleString()} lectures
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Taux de clic</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.clickRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                {summary.totalClicked.toLocaleString()} clics
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Type le plus performant</CardTitle>
              <Bell className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold capitalize">{summary.topPerformingType}</div>
              <p className="text-xs text-muted-foreground">
                Meilleur taux d'engagement
              </p>
            </CardContent>
          </Card>
        </div>
      ) : !settings?.enabled ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Analytics désactivées</h3>
              <p className="text-muted-foreground mb-4">
                Activez les analytics pour voir les statistiques détaillées de vos notifications.
              </p>
              <Button onClick={() => setShowSettings(true)}>
                <Settings className="h-4 w-4 mr-2" />
                Configurer les analytics
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8 text-muted-foreground">
              <BarChart3 className="h-12 w-12 mx-auto mb-4" />
              <p>Aucune donnée d'analytics disponible pour le moment.</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
