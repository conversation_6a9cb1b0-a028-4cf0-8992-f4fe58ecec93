"use client"

import { useState } from "react"
import { useNotifications } from "@/lib/hooks/use-notifications"
import { NotificationPreferences } from "@/components/notification-preferences"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Bell, 
  BellRing, 
  Settings, 
  Search, 
  Filter,
  Check,
  ExternalLink,
  Newspaper,
  FileText,
  Calendar,
  MarkAsRead
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import { cn } from "@/lib/utils"
import Link from "next/link"

export default function NotificationsPage() {
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAsClicked,
    refreshNotifications
  } = useNotifications()

  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")

  // Filtrer les notifications
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.body?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === "all" || notification.type === typeFilter
    const matchesStatus = statusFilter === "all" || 
                         (statusFilter === "read" && notification.isRead) ||
                         (statusFilter === "unread" && !notification.isRead)
    
    return matchesSearch && matchesType && matchesStatus
  })

  const handleNotificationClick = async (notification: any) => {
    // Marquer comme cliquée
    if (notification.id) {
      await markAsClicked(notification.id)
    }

    // Marquer comme lue si pas encore lue
    if (!notification.isRead && notification.id) {
      await markAsRead(notification.id)
    }

    // Naviguer vers le lien si disponible
    if (notification.linkUrl) {
      window.open(notification.linkUrl, '_blank')
    } else if (notification.linkType && notification.linkId) {
      const url = notification.linkType === 'news' 
        ? `/dashboard/news/${notification.linkId}`
        : `/dashboard/pages/${notification.linkId}`
      window.open(url, '_blank')
    }
  }

  const handleMarkAsRead = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    await markAsRead(notificationId)
  }

  const markAllAsRead = async () => {
    const unreadNotifications = notifications.filter(n => !n.isRead && n.id)
    const promises = unreadNotifications.map(n => markAsRead(n.id!))
    await Promise.all(promises)
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'news':
        return <Newspaper className="h-4 w-4" />
      case 'page':
        return <FileText className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      case 'news':
        return 'Actualité'
      case 'page':
        return 'Page'
      case 'system':
        return 'Système'
      default:
        return 'Général'
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Bell className="h-8 w-8" />
            Notifications
          </h1>
          <p className="text-muted-foreground">
            Gérez vos notifications et préférences
          </p>
        </div>
        {unreadCount > 0 && (
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {unreadCount} non lue{unreadCount > 1 ? 's' : ''}
            </Badge>
            <Button variant="outline" onClick={markAllAsRead}>
              <Check className="h-4 w-4 mr-2" />
              Tout marquer comme lu
            </Button>
          </div>
        )}
      </div>

      {/* Onglets */}
      <Tabs defaultValue="notifications" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <BellRing className="h-4 w-4" />
            Mes notifications
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Préférences
          </TabsTrigger>
        </TabsList>

        {/* Liste des notifications */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Mes notifications</CardTitle>
                  <CardDescription>
                    Toutes vos notifications reçues
                  </CardDescription>
                </div>
                <Button variant="outline" onClick={refreshNotifications}>
                  <Bell className="h-4 w-4 mr-2" />
                  Actualiser
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* Filtres */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Rechercher dans les notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-full sm:w-40">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les types</SelectItem>
                    <SelectItem value="general">Général</SelectItem>
                    <SelectItem value="news">Actualité</SelectItem>
                    <SelectItem value="page">Page</SelectItem>
                    <SelectItem value="system">Système</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full sm:w-40">
                    <SelectValue placeholder="Statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes</SelectItem>
                    <SelectItem value="unread">Non lues</SelectItem>
                    <SelectItem value="read">Lues</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Liste des notifications */}
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <Card key={i}>
                      <CardContent className="pt-6">
                        <div className="flex items-start space-x-4">
                          <Skeleton className="h-10 w-10 rounded-full" />
                          <div className="space-y-2 flex-1">
                            <Skeleton className="h-4 w-3/4" />
                            <Skeleton className="h-3 w-1/2" />
                            <Skeleton className="h-3 w-1/4" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : error ? (
                <Card className="border-destructive">
                  <CardContent className="pt-6">
                    <p className="text-sm text-destructive">{error}</p>
                  </CardContent>
                </Card>
              ) : filteredNotifications.length === 0 ? (
                <div className="text-center py-8">
                  <Bell className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    {searchTerm || typeFilter !== "all" || statusFilter !== "all" 
                      ? "Aucune notification ne correspond aux filtres"
                      : "Aucune notification reçue"
                    }
                  </p>
                </div>
              ) : (
                <ScrollArea className="h-[600px]">
                  <div className="space-y-2">
                    {filteredNotifications.map((notification, index) => (
                      <Card 
                        key={notification.id || index}
                        className={cn(
                          "cursor-pointer transition-colors hover:bg-muted/50",
                          !notification.isRead && "border-primary/50 bg-primary/5"
                        )}
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <CardContent className="pt-4 pb-4">
                          <div className="flex items-start space-x-3">
                            <div className={cn(
                              "flex-shrink-0 p-2 rounded-full",
                              notification.isRead ? "bg-muted" : "bg-primary/10"
                            )}>
                              {getNotificationIcon(notification.type || 'general')}
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <p className={cn(
                                    "text-sm font-medium leading-tight",
                                    !notification.isRead && "font-semibold"
                                  )}>
                                    {notification.title || 'Notification'}
                                  </p>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {notification.body || 'Contenu de la notification'}
                                  </p>
                                  
                                  <div className="flex items-center gap-2 mt-2">
                                    <Badge variant="outline" className="text-xs">
                                      {getNotificationTypeLabel(notification.type || 'general')}
                                    </Badge>
                                    {notification.createdAt && (
                                      <span className="text-xs text-muted-foreground">
                                        {formatDistanceToNow(
                                          notification.createdAt.toDate ? 
                                            notification.createdAt.toDate() : 
                                            new Date(notification.createdAt),
                                          { addSuffix: true, locale: fr }
                                        )}
                                      </span>
                                    )}
                                  </div>
                                </div>
                                
                                <div className="flex items-center gap-1 ml-2">
                                  {(notification.linkUrl || notification.linkId) && (
                                    <ExternalLink className="h-3 w-3 text-muted-foreground" />
                                  )}
                                  {!notification.isRead && (
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6"
                                      onClick={(e) => handleMarkAsRead(notification.id!, e)}
                                    >
                                      <Check className="h-3 w-3" />
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Préférences */}
        <TabsContent value="preferences">
          <NotificationPreferences />
        </TabsContent>
      </Tabs>
    </div>
  )
}
