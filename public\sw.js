importScripts("https://storage.googleapis.com/workbox-cdn/releases/7.0.0/workbox-sw.js")

// Vérifier si Workbox a bien été chargé
if (typeof workbox === "undefined") {
  console.error("Workbox n'a pas pu être chargé")
} else {
  console.log("Workbox chargé avec succès")

  // Configuration de Workbox
  workbox.setConfig({
    debug: false,
  })

  const { routing, strategies, precaching, expiration, backgroundSync, cacheableResponse } = workbox

  // Variable pour la simulation du mode hors ligne
  let isOfflineSimulated = false

  // Version du cache
  const CACHE_VERSION = "v2.1.0"
  const CACHE_NAME = `acr-direct-${CACHE_VERSION}`
  const AUTH_CACHE_NAME = `acr-direct-auth-${CACHE_VERSION}`
  const API_CACHE_NAME = `acr-direct-api-${CACHE_VERSION}`
  const IMAGE_CACHE_NAME = `acr-direct-images-${CACHE_VERSION}`

  // Liste des images importantes à précacher
  const IMPORTANT_IMAGES = [
    "/logo-acr-direct.png",
    "/android-chrome-192x192.png",
    "/android-chrome-512x512.png",
    "/apple-touch-icon.png",
    "/favicon-32x32.png",
    "/favicon-16x16.png",
    "/favicon.ico",
    "/logo-acr-direct-dark.png",
    "/logo-acr-direct-light.png",
    "/placeholder.svg",
    // Ajoutez ici d'autres images importantes de votre application
  ]

  // Ressources essentielles à mettre en cache
  const ESSENTIAL_ASSETS = [
    { url: "/", revision: CACHE_VERSION },
    { url: "/offline", revision: CACHE_VERSION },
    { url: "/manifest.json", revision: CACHE_VERSION },
    ...IMPORTANT_IMAGES.map((url) => ({ url, revision: CACHE_VERSION })),
  ]

  // Précacher les ressources essentielles
  precaching.precacheAndRoute(ESSENTIAL_ASSETS)

  // Stratégie pour les pages HTML - StaleWhileRevalidate pour une expérience offline transparente
  // Cette stratégie affiche d'abord le contenu en cache, puis met à jour en arrière-plan
  routing.registerRoute(
    ({ request, url }) => {
      // Vérifier si c'est une requête de navigation
      if (request.mode !== "navigate") return false

      // Vérifier si c'est une page d'actualité ou une page de menu
      const isNewsPage = url.pathname.includes("/dashboard/news/")
      const isMenuPage = url.pathname.includes("/dashboard/pages/")

      // Pour les pages d'actualités et de menu, utiliser une stratégie CacheFirst
      // pour garantir qu'elles fonctionnent hors ligne
      if (isNewsPage || isMenuPage) {
        console.log("[SW] Using CacheFirst strategy for news/menu page:", url.pathname)
        return false // Ne pas utiliser cette route, utiliser la route spécifique ci-dessous
      }

      // Pour les autres pages, utiliser StaleWhileRevalidate
      return true
    },
    new strategies.StaleWhileRevalidate({
      cacheName: `${CACHE_NAME}-pages`,
      plugins: [
        new expiration.ExpirationPlugin({
          maxEntries: 100, // Augmenté pour stocker plus de pages
          maxAgeSeconds: 30 * 24 * 60 * 60, // 30 jours (augmenté pour une meilleure persistance)
          purgeOnQuotaError: true,
        }),
        new cacheableResponse.CacheableResponsePlugin({
          statuses: [0, 200],
        }),
      ],
    }),
  )

  // Stratégie spécifique pour les pages d'actualités et de menu - CacheFirst pour garantir le fonctionnement hors ligne
  routing.registerRoute(
    ({ request, url }) =>
      request.mode === "navigate" &&
      (url.pathname.includes("/dashboard/news/") || url.pathname.includes("/dashboard/pages/")),
    new strategies.CacheFirst({
      cacheName: `${CACHE_NAME}-content-pages`,
      plugins: [
        new expiration.ExpirationPlugin({
          maxEntries: 200, // Plus d'entrées pour stocker plus de pages de contenu
          maxAgeSeconds: 60 * 24 * 60 * 60, // 60 jours pour une meilleure persistance
          purgeOnQuotaError: true,
        }),
        new cacheableResponse.CacheableResponsePlugin({
          statuses: [0, 200],
        }),
      ],
    }),
  )

  // Stratégie pour les ressources statiques (JS, CSS)
  routing.registerRoute(
    ({ request }) => request.destination === "script" || request.destination === "style",
    new strategies.StaleWhileRevalidate({
      cacheName: `${CACHE_NAME}-assets`,
      plugins: [
        new expiration.ExpirationPlugin({
          maxEntries: 100,
          maxAgeSeconds: 30 * 24 * 60 * 60, // 30 jours
          purgeOnQuotaError: true,
        }),
        new cacheableResponse.CacheableResponsePlugin({
          statuses: [0, 200],
        }),
      ],
    }),
  )

  // *** STRATÉGIE CACHE FIRST POUR LES IMAGES ***
  // Cette stratégie garantit que les images apparaissent instantanément après leur premier chargement
  routing.registerRoute(
    ({ request }) => request.destination === "image",
    new strategies.CacheFirst({
      cacheName: IMAGE_CACHE_NAME,
      plugins: [
        new expiration.ExpirationPlugin({
          maxEntries: 200, // Augmenté pour stocker plus d'images
          maxAgeSeconds: 90 * 24 * 60 * 60, // 90 jours
          purgeOnQuotaError: true,
        }),
        new cacheableResponse.CacheableResponsePlugin({
          statuses: [0, 200],
        }),
      ],
    }),
  )

  // Stratégie spécifique pour les images Firebase Storage - également CacheFirst
  routing.registerRoute(
    ({ url }) => url.href.includes("firebasestorage.googleapis.com"),
    new strategies.CacheFirst({
      cacheName: `${IMAGE_CACHE_NAME}-firebase`,
      plugins: [
        new expiration.ExpirationPlugin({
          maxEntries: 150,
          maxAgeSeconds: 30 * 24 * 60 * 60, // 30 jours
          purgeOnQuotaError: true,
        }),
        new cacheableResponse.CacheableResponsePlugin({
          statuses: [0, 200],
        }),
      ],
    }),
  )

  // Stratégie pour les images Next.js optimisées
  routing.registerRoute(
    ({ url }) => url.pathname.startsWith("/_next/image"),
    new strategies.CacheFirst({
      cacheName: `${IMAGE_CACHE_NAME}-next`,
      plugins: [
        new expiration.ExpirationPlugin({
          maxEntries: 100,
          maxAgeSeconds: 30 * 24 * 60 * 60, // 30 jours
          purgeOnQuotaError: true,
        }),
      ],
    }),
  )

  // Stratégie pour les polices - Cache First
  routing.registerRoute(
    ({ request }) => request.destination === "font",
    new strategies.CacheFirst({
      cacheName: `${CACHE_NAME}-fonts`,
      plugins: [
        new expiration.ExpirationPlugin({
          maxEntries: 30,
          maxAgeSeconds: 365 * 24 * 60 * 60, // 1 an
          purgeOnQuotaError: true,
        }),
        new cacheableResponse.CacheableResponsePlugin({
          statuses: [0, 200],
        }),
      ],
    }),
  )

  // Stratégie pour les API Firebase (non-auth) - StaleWhileRevalidate pour une expérience offline transparente
  routing.registerRoute(
    ({ url }) =>
      (url.href.includes("firebaseapp.com") || url.href.includes("googleapis.com")) &&
      !url.href.includes("identitytoolkit") &&
      !url.href.includes("securetoken"),
    new strategies.StaleWhileRevalidate({
      cacheName: API_CACHE_NAME,
      plugins: [
        new expiration.ExpirationPlugin({
          maxEntries: 100, // Augmenté pour stocker plus de réponses API
          maxAgeSeconds: 7 * 24 * 60 * 60, // 7 jours (augmenté pour une meilleure persistance)
          purgeOnQuotaError: true,
        }),
        new cacheableResponse.CacheableResponsePlugin({
          statuses: [0, 200],
        }),
      ],
    }),
  )

  // Stratégie pour les requêtes d'authentification Firebase - NetworkOnly
  routing.registerRoute(
    ({ url }) => url.href.includes("identitytoolkit") || url.href.includes("securetoken"),
    new strategies.NetworkOnly(),
  )

  // Stratégie pour les API internes de l'application - StaleWhileRevalidate pour une expérience offline transparente
  routing.registerRoute(
    ({ url }) =>
      url.pathname.startsWith("/api/") && !url.pathname.includes("/api/auth/") && !url.pathname.includes("/api/admin/"),
    new strategies.StaleWhileRevalidate({
      cacheName: `${API_CACHE_NAME}-internal`,
      plugins: [
        new expiration.ExpirationPlugin({
          maxEntries: 100,
          maxAgeSeconds: 7 * 24 * 60 * 60, // 7 jours
          purgeOnQuotaError: true,
        }),
        new cacheableResponse.CacheableResponsePlugin({
          statuses: [0, 200],
        }),
      ],
    }),
  )

  // Stratégie pour les données d'authentification
  routing.registerRoute(
    ({ url }) => url.pathname === "/auth-data",
    new strategies.StaleWhileRevalidate({
      cacheName: AUTH_CACHE_NAME,
      plugins: [
        new expiration.ExpirationPlugin({
          maxEntries: 10,
          maxAgeSeconds: 30 * 24 * 60 * 60, // 30 jours au lieu de 14
          purgeOnQuotaError: true,
        }),
        new cacheableResponse.CacheableResponsePlugin({
          statuses: [0, 200],
        }),
      ],
    }),
  )

  // Configurer la synchronisation en arrière-plan
  const bgSyncPlugin = new backgroundSync.BackgroundSyncPlugin("offlineQueue", {
    maxRetentionTime: 24 * 60, // 24 heures (en minutes)
  })

  // Stratégie pour les requêtes POST (avec synchronisation en arrière-plan)
  routing.registerRoute(
    ({ request }) => request.method === "POST",
    new strategies.NetworkOnly({
      plugins: [bgSyncPlugin],
    }),
  )

  // Intercepter les requêtes fetch pour simuler le mode hors ligne
  const originalFetch = self.fetch
  self.fetch = async (request, init) => {
    // Si la simulation du mode hors ligne est activée, intercepter toutes les requêtes
    // et retourner une erreur réseau simulée
    if (isOfflineSimulated) {
      console.log(
        "[SW] Simulating offline mode, blocking fetch for:",
        typeof request === "string" ? request : request.url,
      )

      // Vérifier si la ressource est dans le cache
      const cachedResponse = await caches.match(request)
      if (cachedResponse) {
        console.log("[SW] Returning cached response for:", typeof request === "string" ? request : request.url)
        return cachedResponse
      }

      // Si la ressource n'est pas dans le cache, simuler une erreur réseau
      throw new Error("Simulated offline mode")
    }

    // Sinon, utiliser le fetch original
    return originalFetch(request, init)
  }

  // Gestion des requêtes en échec (mode hors ligne)
  routing.setCatchHandler(async ({ event, request }) => {
    // Vérifier si c'est une simulation ou un vrai mode hors ligne
    const isOffline = isOfflineSimulated || !navigator.onLine

    if (isOffline) {
      console.log("[SW] Handling offline request for:", request.url)
    }

    // Pour les pages HTML, essayer de trouver une page en cache qui correspond
    if (request.destination === "document" || request.mode === "navigate") {
      console.log("[SW] Trying to find cached page for:", request.url)

      // Essayer d'abord de trouver la page exacte dans le cache
      const exactMatch = await caches.match(request)
      if (exactMatch) {
        console.log("[SW] Found exact match in cache for:", request.url)
        return exactMatch
      }

      // Si la page exacte n'est pas trouvée, essayer de trouver une page similaire
      // (par exemple, si l'URL contient des paramètres de requête)
      const url = new URL(request.url)
      const pathOnly = url.origin + url.pathname

      // Chercher dans tous les caches
      const cacheNames = await caches.keys()
      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName)
        const keys = await cache.keys()

        // Chercher une correspondance par chemin
        for (const key of keys) {
          const keyUrl = new URL(key.url)
          const keyPathOnly = keyUrl.origin + keyUrl.pathname

          if (keyPathOnly === pathOnly) {
            console.log("[SW] Found similar page in cache:", key.url)
            return await cache.match(key)
          }
        }
      }

      // Si aucune page similaire n'est trouvée, retourner la page d'accueil si elle est en cache
      const homePageMatch = await caches.match("/")
      if (homePageMatch) {
        console.log("[SW] Returning cached home page as fallback")
        return homePageMatch
      }

      // En dernier recours, retourner la page offline
      console.log("[SW] No cached pages found, returning offline page")
      return caches.match("/offline")
    }

    // Pour les images, essayer de trouver une image similaire ou retourner un placeholder
    if (request.destination === "image") {
      console.log("[SW] Image request failed, trying to find similar image or placeholder for:", request.url)

      // Vérifier si c'est une image importante
      for (const importantImage of IMPORTANT_IMAGES) {
        if (request.url.includes(importantImage)) {
          const match = await caches.match(importantImage)
          if (match) {
            console.log("[SW] Found important image match:", importantImage)
            return match
          }
        }
      }

      // Essayer de trouver une image similaire dans le cache
      const url = new URL(request.url)
      const filename = url.pathname.split("/").pop()

      // Chercher dans le cache d'images
      const imageCache = await caches.open(IMAGE_CACHE_NAME)
      const keys = await imageCache.keys()

      // Chercher une correspondance par nom de fichier
      for (const key of keys) {
        const keyUrl = new URL(key.url)
        const keyFilename = keyUrl.pathname.split("/").pop()

        if (keyFilename === filename) {
          console.log("[SW] Found similar image in cache by filename:", keyFilename)
          return await imageCache.match(key)
        }
      }

      // Si c'est un logo, essayer de retourner le logo principal
      if (request.url.includes("logo") || request.url.includes("icon")) {
        const logoMatch = await caches.match("/logo-acr-direct.png")
        if (logoMatch) {
          console.log("[SW] Returning main logo as fallback for:", request.url)
          return logoMatch
        }
      }

      // En dernier recours, retourner une image de placeholder
      console.log("[SW] No similar image found, returning placeholder for:", request.url)
      return caches.match("/placeholder.svg").then((response) => {
        return (
          response ||
          new Response(
            '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#f0f0f0"/><text x="50%" y="50%" font-family="sans-serif" font-size="24" text-anchor="middle" dominant-baseline="middle" fill="#999">Image</text></svg>',
            { headers: { "Content-Type": "image/svg+xml" } },
          )
        )
      })
    }

    // Pour les API, essayer de trouver une réponse en cache
    if (request.url.includes("/api/")) {
      console.log("[SW] API request failed, trying to find cached response for:", request.url)

      // Essayer d'abord de trouver la réponse exacte dans le cache
      const exactMatch = await caches.match(request)
      if (exactMatch) {
        console.log("[SW] Found exact API match in cache")
        return exactMatch
      }

      // Si la réponse exacte n'est pas trouvée, essayer de trouver une réponse similaire
      const url = new URL(request.url)
      const pathOnly = url.origin + url.pathname

      // Chercher dans le cache API
      const apiCache = await caches.open(API_CACHE_NAME)
      const keys = await apiCache.keys()

      // Chercher une correspondance par chemin
      for (const key of keys) {
        const keyUrl = new URL(key.url)
        const keyPathOnly = keyUrl.origin + keyUrl.pathname

        if (keyPathOnly === pathOnly) {
          console.log("[SW] Found similar API response in cache")
          return await apiCache.match(key)
        }
      }

      // Si aucune réponse similaire n'est trouvée, retourner une réponse vide
      console.log("[SW] No cached API response found, returning empty response")
      return new Response(
        JSON.stringify({
          error: "offline",
          message: "Cette fonctionnalité n'est pas disponible hors ligne",
          timestamp: Date.now(),
        }),
        {
          headers: { "Content-Type": "application/json" },
          status: 503,
          statusText: "Service Unavailable (Offline)",
        },
      )
    }

    // Pour les autres types de requêtes, retourner une erreur
    return Response.error()
  })

  // Nettoyage des anciens caches lors de l'activation
  self.addEventListener("activate", (event) => {
    console.log("Service Worker activating with Workbox")

    // Supprimer les anciens caches
    event.waitUntil(
      caches
        .keys()
        .then((cacheNames) => {
          return Promise.all(
            cacheNames.map((cacheName) => {
              // Vérifier si ce n'est pas un cache actuel
              if (!cacheName.includes(CACHE_VERSION) && cacheName.startsWith("acr-direct-")) {
                console.log("Suppression de l'ancien cache:", cacheName)
                return caches.delete(cacheName)
              }
              return Promise.resolve()
            }),
          )
        })
        .then(() => {
          console.log("Nettoyage des caches terminé")
          return self.clients.claim()
        }),
    )
  })

  // Écouter les messages
  self.addEventListener("message", (event) => {
    if (event.data && event.data.type === "SKIP_WAITING") {
      console.log("[SW] Received SKIP_WAITING, activating new SW...")

      // Notifier tous les clients que le service worker va être mis à jour
      self.clients.matchAll().then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: "SW_UPDATING",
            message: "Le service worker va être mis à jour. Préparation de la mise à jour...",
          })
        })

        // Attendre un court instant pour permettre aux clients de se préparer
        setTimeout(() => {
          self.skipWaiting()
        }, 500)
      })
    } else if (event.data && event.data.type === "CHECK_AUTH_STATE") {
      // Vérifier l'état d'authentification après une mise à jour
      console.log("[SW] Received CHECK_AUTH_STATE request")

      // Notifier le client qui a envoyé le message
      if (event.source) {
        event.source.postMessage({
          type: "AUTH_CHECK_REQUESTED",
          timestamp: Date.now(),
        })
      }
    } else if (event.data && event.data.type === "SIMULATE_OFFLINE") {
      // Simuler le mode hors ligne
      isOfflineSimulated = event.data.value === true
      console.log(`[SW] ${isOfflineSimulated ? "Activating" : "Deactivating"} offline simulation mode`)

      // Répondre à tous les clients pour indiquer le changement d'état
      self.clients.matchAll().then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: "OFFLINE_SIMULATION_CHANGED",
            isOfflineSimulated: isOfflineSimulated,
          })
        })
      })

      // Répondre au client qui a envoyé le message
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          success: true,
          isOfflineSimulated: isOfflineSimulated,
        })
      }
    } else if (event.data && event.data.type === "CHECK_OFFLINE_SIMULATION") {
      // Vérifier si la simulation du mode hors ligne est supportée
      console.log("[SW] Checking offline simulation support")

      // Répondre au client qui a envoyé le message
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          supported: true,
          isOfflineSimulated: isOfflineSimulated,
        })
      }
    } else if (event.data && event.data.type === "CACHE_IMAGES") {
      const urls = event.data.urls
      // Utiliser la stratégie CacheFirst pour précharger les images
      const imageCache = caches.open(IMAGE_CACHE_NAME)
      event.waitUntil(
        imageCache.then((cache) => {
          return Promise.all(
            urls.map((url) =>
              fetch(url)
                .then((response) => {
                  if (response.ok) {
                    return cache.put(url, response)
                  }
                  return Promise.resolve()
                })
                .catch((error) => console.error(`Erreur lors du préchargement de l'image ${url}:`, error)),
            ),
          )
        }),
      )

      // Répondre au client qui a envoyé le message
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          success: true,
          message: `${urls.length} images mises en cache`,
        })
      }
    } else if (event.data && event.data.type === "CACHE_PAGES") {
      const urls = event.data.urls
      console.log("[SW] Caching pages:", urls)

      // Utiliser la stratégie StaleWhileRevalidate pour précharger les pages
      // pour une expérience offline transparente
      const pageCache = caches.open(`${CACHE_NAME}-pages`)
      event.waitUntil(
        pageCache.then((cache) => {
          return Promise.all(
            urls.map((url) =>
              fetch(url, {
                credentials: "include", // Inclure les cookies pour les pages authentifiées
                cache: "reload", // Forcer le rechargement pour obtenir la version la plus récente
              })
                .then((response) => {
                  if (response.ok) {
                    console.log(`[SW] Page mise en cache avec succès: ${url}`)
                    return cache.put(url, response)
                  }
                  console.warn(`[SW] Échec de mise en cache de la page: ${url}, status: ${response.status}`)
                  return Promise.resolve()
                })
                .catch((error) => {
                  console.error(`[SW] Erreur lors du préchargement de la page ${url}:`, error)
                  return Promise.resolve() // Continuer malgré l'erreur
                }),
            ),
          )
        }),
      )

      // Répondre au client qui a envoyé le message
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          success: true,
          message: `${urls.length} pages mises en cache`,
        })
      }
    } else if (event.data && event.data.type === "CACHE_AUTH_DATA") {
      const authData = event.data.authData
      console.log("[SW] Caching auth data for user:", authData.uid ? authData.uid.substring(0, 5) + "..." : "unknown")

      event.waitUntil(
        caches.open(AUTH_CACHE_NAME).then((cache) => {
          return cache.put(
            "/auth-data",
            new Response(
              JSON.stringify({
                ...authData,
                timestamp: Date.now(),
                cachedBy: "service-worker",
              }),
              {
                headers: { "Content-Type": "application/json" },
              },
            ),
          )
        }),
      )

      // Répondre au client qui a envoyé le message
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          success: true,
          message: "Données d'authentification mises en cache",
        })
      }
    } else if (event.data && event.data.type === "CLEAR_CACHE") {
      // Fonction pour vider un cache spécifique ou tous les caches
      const cacheName = event.data.cacheName
      if (cacheName) {
        event.waitUntil(caches.delete(cacheName))
      } else {
        event.waitUntil(
          caches.keys().then((cacheNames) => {
            return Promise.all(cacheNames.map((name) => caches.delete(name)))
          }),
        )
      }

      // Répondre au client qui a envoyé le message
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          success: true,
          message: cacheName ? `Cache ${cacheName} vidé` : "Tous les caches vidés",
        })
      }
    } else if (event.data && event.data.type === "CACHE_API") {
      // Précharger les données d'API
      const apiUrls = event.data.urls
      console.log("[SW] Caching API endpoints:", apiUrls)

      // Utiliser la stratégie StaleWhileRevalidate pour précharger les API
      const apiCache = caches.open(API_CACHE_NAME)
      event.waitUntil(
        apiCache.then((cache) => {
          return Promise.all(
            apiUrls.map((url) =>
              fetch(url, {
                credentials: "include", // Inclure les cookies pour les API authentifiées
                cache: "reload", // Forcer le rechargement pour obtenir la version la plus récente
              })
                .then((response) => {
                  if (response.ok) {
                    console.log(`[SW] API mise en cache avec succès: ${url}`)
                    return cache.put(url, response.clone())
                  }
                  console.warn(`[SW] Échec de mise en cache de l'API: ${url}, status: ${response.status}`)
                  return Promise.resolve()
                })
                .catch((error) => {
                  console.error(`[SW] Erreur lors du préchargement de l'API ${url}:`, error)
                  return Promise.resolve() // Continuer malgré l'erreur
                }),
            ),
          )
        }),
      )

      // Répondre au client qui a envoyé le message
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          success: true,
          message: `${apiUrls.length} endpoints API mis en cache`,
        })
      }
    } else if (event.data && event.data.type === "PRELOAD_ALL") {
      // Précharger toutes les ressources importantes pour le mode hors ligne
      console.log("[SW] Preloading all important resources for offline mode")

      const { pages, apis, images } = event.data
      let totalResources = 0
      let cachedResources = 0

      // Fonction pour mettre à jour le statut
      const updateStatus = () => {
        if (event.ports && event.ports[0]) {
          event.ports[0].postMessage({
            type: "PRELOAD_STATUS",
            cachedResources,
            totalResources,
            progress: totalResources > 0 ? Math.round((cachedResources / totalResources) * 100) : 0,
          })
        }
      }

      // Calculer le nombre total de ressources
      totalResources = (pages?.length || 0) + (apis?.length || 0) + (images?.length || 0)
      updateStatus()

      // Précharger les pages
      if (pages && pages.length > 0) {
        const pageCache = caches.open(`${CACHE_NAME}-pages`)
        event.waitUntil(
          pageCache.then((cache) => {
            return Promise.all(
              pages.map((url) =>
                fetch(url, { credentials: "include", cache: "reload" })
                  .then((response) => {
                    if (response.ok) {
                      cachedResources++
                      updateStatus()
                      return cache.put(url, response)
                    }
                    return Promise.resolve()
                  })
                  .catch(() => {
                    cachedResources++ // Compter même en cas d'erreur pour maintenir le compteur
                    updateStatus()
                    return Promise.resolve()
                  }),
              ),
            )
          }),
        )
      }

      // Précharger les API
      if (apis && apis.length > 0) {
        const apiCache = caches.open(API_CACHE_NAME)
        event.waitUntil(
          apiCache.then((cache) => {
            return Promise.all(
              apis.map((url) =>
                fetch(url, { credentials: "include", cache: "reload" })
                  .then((response) => {
                    if (response.ok) {
                      cachedResources++
                      updateStatus()
                      return cache.put(url, response)
                    }
                    return Promise.resolve()
                  })
                  .catch(() => {
                    cachedResources++ // Compter même en cas d'erreur pour maintenir le compteur
                    updateStatus()
                    return Promise.resolve()
                  }),
              ),
            )
          }),
        )
      }

      // Précharger les images
      if (images && images.length > 0) {
        const imageCache = caches.open(IMAGE_CACHE_NAME)
        event.waitUntil(
          imageCache.then((cache) => {
            return Promise.all(
              images.map((url) =>
                fetch(url)
                  .then((response) => {
                    if (response.ok) {
                      cachedResources++
                      updateStatus()
                      return cache.put(url, response)
                    }
                    return Promise.resolve()
                  })
                  .catch(() => {
                    cachedResources++ // Compter même en cas d'erreur pour maintenir le compteur
                    updateStatus()
                    return Promise.resolve()
                  }),
              ),
            )
          }),
        )
      }

      // Répondre au client que le préchargement a commencé
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          success: true,
          message: `Préchargement de ${totalResources} ressources démarré`,
          totalResources,
        })
      }
    } else if (event.data && event.data.type === "PRESERVE_AUTH_ON_RELOAD") {
      // Préserver les données d'authentification lors d'un rechargement
      console.log("[SW] Preserving auth data for reload")

      // Stocker temporairement les données d'authentification
      const authData = event.data.authData
      if (authData) {
        self.authDataForReload = authData

        // Notifier le client que les données ont été préservées
        if (event.source) {
          event.source.postMessage({
            type: "AUTH_DATA_PRESERVED",
            timestamp: Date.now(),
          })
        }
      }
    }
  })

  // Gestion des notifications push
  self.addEventListener("push", (event) => {
    console.log("[SW] Push event received:", event)

    if (event.data) {
      try {
        const data = event.data.json()
        console.log("[SW] Push data:", data)

        // Configuration des options de notification
        const options = {
          body: data.body || data.notification?.body || "Nouvelle notification",
          icon: data.icon || data.notification?.icon || "/android-chrome-192x192.png",
          badge: data.badge || "/favicon-32x32.png",
          image: data.image || data.notification?.image,
          tag: data.tag || data.notification?.tag || "acr-direct-notification",
          renotify: true,
          requireInteraction: data.priority === "high",
          silent: data.priority === "low",
          timestamp: Date.now(),
          data: {
            url: data.click_action || data.data?.url || data.url || "/dashboard/notifications",
            notificationId: data.notificationId || data.data?.notificationId,
            type: data.type || data.data?.type || "general",
            linkType: data.linkType || data.data?.linkType,
            linkId: data.linkId || data.data?.linkId,
            ...data.data
          },
          actions: data.actions || [
            {
              action: "open",
              title: "Ouvrir",
              icon: "/android-chrome-192x192.png"
            },
            {
              action: "dismiss",
              title: "Ignorer"
            }
          ],
          vibrate: data.priority === "high" ? [200, 100, 200] : [100],
        }

        // Ajouter des actions spécifiques selon le type
        if (data.type === "news" && data.linkId) {
          options.actions.unshift({
            action: "read_news",
            title: "Lire l'article",
            icon: "/android-chrome-192x192.png"
          })
        } else if (data.type === "page" && data.linkId) {
          options.actions.unshift({
            action: "view_page",
            title: "Voir la page",
            icon: "/android-chrome-192x192.png"
          })
        }

        const title = data.title || data.notification?.title || "ACR Direct"

        event.waitUntil(
          self.registration.showNotification(title, options)
            .then(() => {
              console.log("[SW] Notification displayed successfully")

              // Envoyer un message à l'application pour mettre à jour le compteur
              return self.clients.matchAll({ type: "window" }).then(clients => {
                clients.forEach(client => {
                  client.postMessage({
                    type: "NOTIFICATION_RECEIVED",
                    data: data
                  })
                })
              })
            })
            .catch(error => {
              console.error("[SW] Error displaying notification:", error)
            })
        )
      } catch (error) {
        console.error("[SW] Error parsing push data:", error)

        // Afficher une notification par défaut en cas d'erreur
        event.waitUntil(
          self.registration.showNotification("ACR Direct", {
            body: "Nouvelle notification",
            icon: "/android-chrome-192x192.png",
            badge: "/favicon-32x32.png",
            data: { url: "/dashboard/notifications" }
          })
        )
      }
    } else {
      console.log("[SW] Push event without data")

      // Notification par défaut sans données
      event.waitUntil(
        self.registration.showNotification("ACR Direct", {
          body: "Vous avez reçu une nouvelle notification",
          icon: "/android-chrome-192x192.png",
          badge: "/favicon-32x32.png",
          data: { url: "/dashboard/notifications" }
        })
      )
    }
  })

  // Gestion des clics sur les notifications
  self.addEventListener("notificationclick", (event) => {
    console.log("[SW] Notification click:", event.action, event.notification.data)
    event.notification.close()

    const notificationData = event.notification.data || {}
    let targetUrl = notificationData.url || "/dashboard/notifications"

    // Gérer les actions spécifiques
    if (event.action) {
      console.log(`[SW] Notification action: ${event.action}`)

      switch (event.action) {
        case "read_news":
          if (notificationData.linkId) {
            targetUrl = `/dashboard/news/${notificationData.linkId}`
          }
          break
        case "view_page":
          if (notificationData.linkId) {
            targetUrl = `/dashboard/pages/${notificationData.linkId}`
          }
          break
        case "open":
          // Utiliser l'URL par défaut
          break
        case "dismiss":
          // Ne rien faire, juste fermer
          return
        default:
          console.log(`[SW] Unknown action: ${event.action}`)
      }
    } else {
      // Clic direct sur la notification (pas sur une action)
      if (notificationData.linkType && notificationData.linkId) {
        if (notificationData.linkType === "news") {
          targetUrl = `/dashboard/news/${notificationData.linkId}`
        } else if (notificationData.linkType === "page") {
          targetUrl = `/dashboard/pages/${notificationData.linkId}`
        }
      } else if (notificationData.url) {
        targetUrl = notificationData.url
      }
    }

    console.log(`[SW] Opening URL: ${targetUrl}`)

    // Ouvrir l'URL dans une fenêtre
    event.waitUntil(
      clients.matchAll({ type: "window", includeUncontrolled: true }).then((windowClients) => {
        console.log(`[SW] Found ${windowClients.length} window clients`)

        // Chercher une fenêtre existante avec la même origine
        for (const client of windowClients) {
          const clientUrl = new URL(client.url)
          const targetUrlObj = new URL(targetUrl, self.location.origin)

          if (clientUrl.origin === targetUrlObj.origin) {
            console.log(`[SW] Focusing existing window and navigating to: ${targetUrl}`)

            // Envoyer un message pour naviguer vers la bonne page
            client.postMessage({
              type: "NOTIFICATION_CLICK",
              url: targetUrl,
              notificationData: notificationData
            })

            return client.focus()
          }
        }

        // Aucune fenêtre trouvée, en ouvrir une nouvelle
        console.log(`[SW] Opening new window: ${targetUrl}`)
        if (clients.openWindow) {
          return clients.openWindow(targetUrl)
        }
      }).catch(error => {
        console.error("[SW] Error handling notification click:", error)
      })
    )

    // Marquer la notification comme cliquée (optionnel)
    if (notificationData.notificationId) {
      // Envoyer un message à l'application pour marquer comme cliquée
      event.waitUntil(
        clients.matchAll({ type: "window" }).then(clients => {
          clients.forEach(client => {
            client.postMessage({
              type: "NOTIFICATION_CLICKED",
              notificationId: notificationData.notificationId
            })
          })
        })
      )
    }
  })

  // Installation du service worker
  self.addEventListener("install", (event) => {
    console.log("Service Worker installing with Workbox")
    self.skipWaiting()
  })
}

// Add this function if hash functionality is needed:
function simpleHash(str) {
  let hash = 0
  if (str.length === 0) return hash
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // Convert to 32bit integer
  }
  return hash.toString()
}
