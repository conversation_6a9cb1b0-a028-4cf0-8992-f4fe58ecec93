"use client"

import { useState } from "react"
import { useNotifications } from "@/lib/hooks/use-notifications"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { Skeleton } from "@/components/ui/skeleton"
import { Bell, BellRing, Check, Settings, X, ExternalLink, Newspaper, FileText } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface NotificationCenterProps {
  className?: string
  showBadge?: boolean
  variant?: "icon" | "button"
}

export function NotificationCenter({
  className,
  showBadge = true,
  variant = "icon"
}: NotificationCenterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)

  // Charger les notifications seulement quand nécessaire
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAsClicked,
    refreshNotifications
  } = hasInitialized ? useNotifications() : {
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null,
    markAsRead: async () => {},
    markAsClicked: async () => {},
    refreshNotifications: () => {}
  }

  // Initialiser les notifications seulement quand l'utilisateur ouvre le centre
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    if (open && !hasInitialized) {
      setHasInitialized(true)
    }
  }

  const handleNotificationClick = async (notification: any) => {
    // Marquer comme cliquée
    if (notification.id) {
      await markAsClicked(notification.id)
    }

    // Marquer comme lue si pas encore lue
    if (!notification.isRead && notification.id) {
      await markAsRead(notification.id)
    }

    // Naviguer vers le lien si disponible
    if (notification.linkUrl) {
      window.open(notification.linkUrl, '_blank')
    } else if (notification.linkType && notification.linkId) {
      const url = notification.linkType === 'news'
        ? `/dashboard/news/${notification.linkId}`
        : `/dashboard/pages/${notification.linkId}`
      window.open(url, '_blank')
    }

    setIsOpen(false)
  }

  const handleMarkAsRead = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    await markAsRead(notificationId)
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'news':
        return <Newspaper className="h-4 w-4" />
      case 'page':
        return <FileText className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      case 'news':
        return 'Actualité'
      case 'page':
        return 'Page'
      case 'system':
        return 'Système'
      default:
        return 'Général'
    }
  }

  const TriggerButton = () => {
    if (variant === "button") {
      return (
        <Button variant="outline" className={cn("relative", className)}>
          <Bell className="h-4 w-4 mr-2" />
          Notifications
          {showBadge && unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      )
    }

    return (
      <Button variant="ghost" size="icon" className={cn("relative", className)}>
        {unreadCount > 0 ? (
          <BellRing className="h-5 w-5" />
        ) : (
          <Bell className="h-5 w-5" />
        )}
        {showBadge && unreadCount > 0 && (
          <Badge
            variant="destructive"
            className="absolute -top-1 -right-1 h-4 w-4 flex items-center justify-center p-0 text-xs"
          >
            {unreadCount > 99 ? "99+" : unreadCount}
          </Badge>
        )}
      </Button>
    )
  }

  return (
    <Sheet open={isOpen} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        <TriggerButton />
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center justify-between">
            <span>Notifications</span>
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <Badge variant="secondary">
                  {unreadCount} non lue{unreadCount > 1 ? 's' : ''}
                </Badge>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={refreshNotifications}
                className="h-8 w-8"
                title="Actualiser"
              >
                <Bell className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                asChild
                className="h-8 w-8"
                title="Préférences"
              >
                <Link href="/dashboard/notifications">
                  <Settings className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </SheetTitle>
          <SheetDescription>
            Vos dernières notifications et mises à jour
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6">
          {error && (
            <Card className="border-destructive">
              <CardContent className="pt-6">
                <p className="text-sm text-destructive">{error}</p>
              </CardContent>
            </Card>
          )}

          <ScrollArea className="h-[calc(100vh-200px)]">
            {loading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Card key={i}>
                    <CardContent className="pt-6">
                      <div className="flex items-start space-x-4">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div className="space-y-2 flex-1">
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-3 w-1/2" />
                          <Skeleton className="h-3 w-1/4" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : notifications.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center py-8">
                    <Bell className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">Aucune notification</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-2">
                {notifications.map((notification, index) => (
                  <Card
                    key={notification.id || index}
                    className={cn(
                      "cursor-pointer transition-colors hover:bg-muted/50",
                      !notification.isRead && "border-primary/50 bg-primary/5"
                    )}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <CardContent className="pt-4 pb-4">
                      <div className="flex items-start space-x-3">
                        <div className={cn(
                          "flex-shrink-0 p-2 rounded-full",
                          notification.isRead ? "bg-muted" : "bg-primary/10"
                        )}>
                          {getNotificationIcon(notification.type || 'general')}
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className={cn(
                                "text-sm font-medium leading-tight",
                                !notification.isRead && "font-semibold"
                              )}>
                                {notification.title || 'Notification'}
                              </p>
                              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                {notification.body || 'Contenu de la notification'}
                              </p>

                              <div className="flex items-center gap-2 mt-2">
                                <Badge variant="outline" className="text-xs">
                                  {getNotificationTypeLabel(notification.type || 'general')}
                                </Badge>
                                {notification.createdAt && (
                                  <span className="text-xs text-muted-foreground">
                                    {formatDistanceToNow(
                                      notification.createdAt.toDate ?
                                        notification.createdAt.toDate() :
                                        new Date(notification.createdAt),
                                      { addSuffix: true, locale: fr }
                                    )}
                                  </span>
                                )}
                              </div>
                            </div>

                            <div className="flex items-center gap-1 ml-2">
                              {(notification.linkUrl || notification.linkId) && (
                                <ExternalLink className="h-3 w-3 text-muted-foreground" />
                              )}
                              {!notification.isRead && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                  onClick={(e) => handleMarkAsRead(notification.id!, e)}
                                >
                                  <Check className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </ScrollArea>

          {notifications.length > 0 && (
            <>
              <Separator className="my-4" />
              <div className="flex justify-center">
                <Link href="/dashboard/notifications">
                  <Button variant="outline" size="sm">
                    Voir toutes les notifications
                  </Button>
                </Link>
              </div>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  )
}
