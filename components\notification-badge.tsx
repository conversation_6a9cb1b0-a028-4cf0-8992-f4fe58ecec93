"use client"

import { useUnreadNotificationCount } from "@/lib/hooks/use-notifications"
import { Badge } from "@/components/ui/badge"
import { Bell, BellRing } from "lucide-react"
import { cn } from "@/lib/utils"

interface NotificationBadgeProps {
  className?: string
  showIcon?: boolean
  showCount?: boolean
  variant?: "default" | "destructive" | "outline" | "secondary"
  size?: "sm" | "default" | "lg"
}

export function NotificationBadge({
  className,
  showIcon = true,
  showCount = true,
  variant = "destructive",
  size = "default"
}: NotificationBadgeProps) {
  const { unreadCount, loading } = useUnreadNotificationCount()

  // Toujours afficher l'icône, même pendant le chargement
  if (loading) {
    return showIcon ? (
      <Bell className={cn(
        "h-4 w-4",
        size === "sm" && "h-3 w-3",
        size === "lg" && "h-5 w-5",
        className
      )} />
    ) : null
  }

  if (unreadCount === 0) {
    return showIcon ? (
      <Bell className={cn(
        "h-4 w-4",
        size === "sm" && "h-3 w-3",
        size === "lg" && "h-5 w-5",
        className
      )} />
    ) : null
  }

  const badgeSize = size === "sm" ? "text-xs" : size === "lg" ? "text-sm" : "text-xs"
  const iconSize = size === "sm" ? "h-3 w-3" : size === "lg" ? "h-5 w-5" : "h-4 w-4"

  return (
    <div className={cn("relative inline-flex", className)}>
      {showIcon && (
        <BellRing className={cn(iconSize, "text-primary")} />
      )}
      {showCount && (
        <Badge
          variant={variant}
          className={cn(
            "absolute -top-1 -right-1 h-4 w-4 flex items-center justify-center p-0",
            badgeSize,
            !showIcon && "relative top-0 right-0"
          )}
        >
          {unreadCount > 99 ? "99+" : unreadCount}
        </Badge>
      )}
    </div>
  )
}

interface NotificationIndicatorProps {
  className?: string
  size?: number
}

export function NotificationIndicator({
  className,
  size = 8
}: NotificationIndicatorProps) {
  const { unreadCount, loading } = useUnreadNotificationCount()

  if (loading || unreadCount === 0) {
    return null
  }

  return (
    <div
      className={cn(
        "absolute bg-red-500 rounded-full animate-pulse",
        className
      )}
      style={{
        width: size,
        height: size,
        top: -size / 4,
        right: -size / 4
      }}
    />
  )
}
