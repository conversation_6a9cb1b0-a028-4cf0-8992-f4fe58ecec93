"use client"

import { useEffect } from "react"
import { useAuth } from "@/lib/hooks/use-auth"
import { useNotifications } from "@/lib/hooks/use-notifications"
import { toast } from "sonner"

interface NotificationProviderProps {
  children: React.ReactNode
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const { user } = useAuth()
  const { initializePushNotifications } = useNotifications()

  useEffect(() => {
    if (!user?.uid) return

    // Initialiser les notifications push de manière non-bloquante
    const initializeNotifications = async () => {
      try {
        // Vérifier si les notifications sont supportées
        if (!('Notification' in window) || !('serviceWorker' in navigator)) {
          console.log("Notifications push non supportées")
          return
        }

        // Vérifier la permission actuelle
        const permission = Notification.permission

        if (permission === 'granted') {
          // Permission déjà accordée, initialiser FCM avec délai pour ne pas bloquer
          setTimeout(async () => {
            try {
              await initializePushNotifications()
            } catch (error) {
              console.warn("Erreur lors de l'initialisation des notifications:", error)
            }
          }, 2000) // Délai pour permettre au dashboard de se charger d'abord
        } else if (permission === 'default') {
          // Demander la permission après un délai plus long
          setTimeout(async () => {
            try {
              const success = await initializePushNotifications()
              if (success) {
                toast.success("Notifications activées avec succès!")
              }
            } catch (error) {
              console.warn("Erreur lors de l'initialisation des notifications:", error)
            }
          }, 5000) // Attendre 5 secondes après le chargement complet
        }
        // Si permission === 'denied', ne rien faire

        // Écouter les messages du service worker
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.addEventListener('message', (event) => {
            console.log("Message du service worker:", event.data)

            switch (event.data.type) {
              case 'NOTIFICATION_RECEIVED':
                // Une nouvelle notification a été reçue
                console.log("Nouvelle notification reçue:", event.data.data)
                break

              case 'NOTIFICATION_CLICKED':
                // Une notification a été cliquée
                console.log("Notification cliquée:", event.data.notificationId)
                break

              case 'NOTIFICATION_CLICK':
                // Navigation demandée depuis le service worker
                if (event.data.url) {
                  window.location.href = event.data.url
                }
                break

              default:
                console.log("Message non géré du service worker:", event.data)
            }
          })
        }

      } catch (error) {
        console.error("Erreur lors de l'initialisation des notifications:", error)
      }
    }

    initializeNotifications()
  }, [user?.uid, initializePushNotifications])

  // Gérer la visibilité de la page pour marquer les notifications comme lues
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && user?.uid) {
        // La page est devenue visible, rafraîchir les notifications
        // Ceci sera géré automatiquement par le hook useNotifications
        console.log("Page visible, les notifications seront rafraîchies automatiquement")
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [user?.uid])

  // Gérer les notifications en temps réel quand l'app est ouverte
  useEffect(() => {
    if (!user?.uid) return

    // Écouter les messages FCM en temps réel
    const handleForegroundMessage = (payload: any) => {
      console.log("Message FCM reçu en premier plan:", payload)

      // Afficher une notification toast dans l'app
      if (payload.notification) {
        toast(payload.notification.title, {
          description: payload.notification.body,
          action: payload.data?.url ? {
            label: "Voir",
            onClick: () => {
              if (payload.data.linkType === 'news' && payload.data.linkId) {
                window.open(`/dashboard/news/${payload.data.linkId}`, '_blank')
              } else if (payload.data.linkType === 'page' && payload.data.linkId) {
                window.open(`/dashboard/pages/${payload.data.linkId}`, '_blank')
              } else if (payload.data.url) {
                window.open(payload.data.url, '_blank')
              }
            }
          } : undefined,
          duration: 5000,
        })
      }
    }

    // Cette fonction sera appelée par le hook useNotifications
    // via NotificationService.onMessage()

    return () => {
      // Cleanup sera géré par le hook useNotifications
    }
  }, [user?.uid])

  return <>{children}</>
}

// Hook pour vérifier le support des notifications
export function useNotificationSupport() {
  const isSupported = typeof window !== 'undefined' &&
                     'Notification' in window &&
                     'serviceWorker' in navigator

  const permission = typeof window !== 'undefined' ?
                    Notification.permission :
                    'default'

  return {
    isSupported,
    permission,
    isGranted: permission === 'granted',
    isDenied: permission === 'denied',
    isDefault: permission === 'default'
  }
}

// Utilitaire pour demander la permission de notification
export async function requestNotificationPermission(): Promise<boolean> {
  if (!('Notification' in window)) {
    console.warn("Notifications non supportées")
    return false
  }

  if (Notification.permission === 'granted') {
    return true
  }

  if (Notification.permission === 'denied') {
    console.warn("Permission de notification refusée")
    return false
  }

  try {
    const permission = await Notification.requestPermission()
    return permission === 'granted'
  } catch (error) {
    console.error("Erreur lors de la demande de permission:", error)
    return false
  }
}

// Utilitaire pour afficher une notification de test
export function showTestNotification() {
  if (!('Notification' in window)) {
    toast.error("Notifications non supportées")
    return
  }

  if (Notification.permission !== 'granted') {
    toast.error("Permission de notification requise")
    return
  }

  try {
    const notification = new Notification("Test ACR Direct", {
      body: "Ceci est une notification de test",
      icon: "/android-chrome-192x192.png",
      badge: "/favicon-32x32.png",
      tag: "test-notification",
      requireInteraction: false,
      silent: false
    })

    notification.onclick = () => {
      window.focus()
      notification.close()
    }

    // Fermer automatiquement après 5 secondes
    setTimeout(() => {
      notification.close()
    }, 5000)

    toast.success("Notification de test envoyée!")
  } catch (error) {
    console.error("Erreur lors de l'affichage de la notification de test:", error)
    toast.error("Impossible d'afficher la notification de test")
  }
}
