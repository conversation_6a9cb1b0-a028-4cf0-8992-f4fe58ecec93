// Firebase Cloud Functions pour les notifications programmées
// Ce fichier doit être déployé dans Firebase Functions

const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Initialiser Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();
const messaging = admin.messaging();

/**
 * Fonction Cloud programmée pour envoyer les notifications
 * S'exécute toutes les minutes pour vérifier les notifications à envoyer
 */
exports.sendScheduledNotifications = functions.pubsub
  .schedule('every 1 minutes')
  .onRun(async (context) => {
    console.log('Vérification des notifications programmées...');
    
    try {
      const now = admin.firestore.Timestamp.now();
      
      // Récupérer les notifications programmées qui doivent être envoyées
      const scheduledNotificationsQuery = await db.collection('notifications')
        .where('status', '==', 'scheduled')
        .where('scheduledAt', '<=', now)
        .get();

      if (scheduledNotificationsQuery.empty) {
        console.log('Aucune notification programmée à envoyer');
        return null;
      }

      console.log(`${scheduledNotificationsQuery.size} notification(s) à envoyer`);

      // Traiter chaque notification
      const promises = scheduledNotificationsQuery.docs.map(async (notificationDoc) => {
        const notificationData = notificationDoc.data();
        const notificationId = notificationDoc.id;

        try {
          // Récupérer les utilisateurs cibles
          let targetUserIds = [];

          if (notificationData.targetType === 'all') {
            const usersSnapshot = await db.collection('users').get();
            targetUserIds = usersSnapshot.docs.map(doc => doc.id);
          } else if (notificationData.targetType === 'groups' && notificationData.targetGroups?.length) {
            const usersSnapshot = await db.collection('users')
              .where('groups', 'array-contains-any', notificationData.targetGroups)
              .get();
            targetUserIds = usersSnapshot.docs.map(doc => doc.id);
          } else if (notificationData.targetType === 'users' && notificationData.targetUsers?.length) {
            targetUserIds = notificationData.targetUsers;
          }

          if (targetUserIds.length === 0) {
            console.log(`Aucun utilisateur cible pour la notification ${notificationId}`);
            await notificationDoc.ref.update({
              status: 'failed',
              error: 'Aucun utilisateur cible trouvé'
            });
            return;
          }

          // Créer les notifications utilisateur
          const batch = db.batch();
          targetUserIds.forEach(userId => {
            const userNotificationRef = db.collection('userNotifications').doc();
            batch.set(userNotificationRef, {
              notificationId,
              userId,
              isRead: false,
              isClicked: false,
              createdAt: admin.firestore.FieldValue.serverTimestamp()
            });
          });
          await batch.commit();

          // Récupérer les tokens FCM (par lots de 10 pour respecter les limites Firestore)
          const allTokens = [];
          for (let i = 0; i < targetUserIds.length; i += 10) {
            const userBatch = targetUserIds.slice(i, i + 10);
            const tokensSnapshot = await db.collection('userTokens')
              .where('userId', 'in', userBatch)
              .get();
            
            const batchTokens = tokensSnapshot.docs
              .map(doc => doc.data().fcmToken)
              .filter(token => token);
            
            allTokens.push(...batchTokens);
          }

          // Envoyer les notifications push
          if (allTokens.length > 0) {
            // Construire l'URL de destination
            let clickAction = '/dashboard/notifications';
            if (notificationData.linkType === 'news' && notificationData.linkId) {
              clickAction = `/dashboard/news/${notificationData.linkId}`;
            } else if (notificationData.linkType === 'page' && notificationData.linkId) {
              clickAction = `/dashboard/pages/${notificationData.linkId}`;
            } else if (notificationData.linkUrl) {
              clickAction = notificationData.linkUrl;
            }

            const message = {
              notification: {
                title: notificationData.title,
                body: notificationData.body,
              },
              data: {
                notificationId,
                type: notificationData.type,
                linkType: notificationData.linkType || 'none',
                linkId: notificationData.linkId || '',
                url: clickAction,
                priority: notificationData.priority
              },
              webpush: {
                notification: {
                  icon: '/android-chrome-192x192.png',
                  badge: '/favicon-32x32.png',
                  tag: 'acr-direct-notification',
                  requireInteraction: notificationData.priority === 'high',
                  silent: notificationData.priority === 'low',
                  actions: [
                    {
                      action: 'open',
                      title: 'Ouvrir'
                    },
                    {
                      action: 'dismiss',
                      title: 'Ignorer'
                    }
                  ]
                },
                fcmOptions: {
                  link: clickAction
                }
              },
              tokens: allTokens
            };

            // Ajouter des actions spécifiques selon le type
            if (notificationData.type === 'news' && notificationData.linkId) {
              message.webpush.notification.actions.unshift({
                action: 'read_news',
                title: 'Lire l\'article'
              });
            } else if (notificationData.type === 'page' && notificationData.linkId) {
              message.webpush.notification.actions.unshift({
                action: 'view_page',
                title: 'Voir la page'
              });
            }

            const response = await messaging.sendEachForMulticast(message);
            
            console.log(`Notification ${notificationId}: ${response.successCount}/${allTokens.length} envoyées`);

            // Mettre à jour le statut
            await notificationDoc.ref.update({
              status: 'sent',
              sentCount: targetUserIds.length,
              pushSentCount: response.successCount,
              pushFailedCount: response.failureCount,
              sentAt: admin.firestore.FieldValue.serverTimestamp()
            });
          } else {
            // Pas de tokens FCM, mais les notifications in-app sont créées
            await notificationDoc.ref.update({
              status: 'sent',
              sentCount: targetUserIds.length,
              pushSentCount: 0,
              sentAt: admin.firestore.FieldValue.serverTimestamp()
            });
          }

        } catch (error) {
          console.error(`Erreur lors de l'envoi de la notification ${notificationId}:`, error);
          await notificationDoc.ref.update({
            status: 'failed',
            error: error.message
          });
        }
      });

      await Promise.all(promises);
      console.log('Traitement des notifications programmées terminé');

    } catch (error) {
      console.error('Erreur lors du traitement des notifications programmées:', error);
    }

    return null;
  });

/**
 * Fonction pour nettoyer les anciennes notifications
 * S'exécute tous les jours à minuit
 */
exports.cleanupOldNotifications = functions.pubsub
  .schedule('0 0 * * *')
  .timeZone('Europe/Paris')
  .onRun(async (context) => {
    console.log('Nettoyage des anciennes notifications...');
    
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const cutoffTimestamp = admin.firestore.Timestamp.fromDate(thirtyDaysAgo);

      // Supprimer les notifications utilisateur anciennes
      const oldUserNotificationsQuery = await db.collection('userNotifications')
        .where('createdAt', '<', cutoffTimestamp)
        .limit(500) // Traiter par lots pour éviter les timeouts
        .get();

      if (!oldUserNotificationsQuery.empty) {
        const batch = db.batch();
        oldUserNotificationsQuery.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        console.log(`${oldUserNotificationsQuery.size} notifications utilisateur supprimées`);
      }

      // Supprimer les notifications expirées
      const expiredNotificationsQuery = await db.collection('notifications')
        .where('expiresAt', '<', admin.firestore.Timestamp.now())
        .limit(100)
        .get();

      if (!expiredNotificationsQuery.empty) {
        const batch = db.batch();
        expiredNotificationsQuery.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        console.log(`${expiredNotificationsQuery.size} notifications expirées supprimées`);
      }

      console.log('Nettoyage terminé');
    } catch (error) {
      console.error('Erreur lors du nettoyage:', error);
    }

    return null;
  });

/**
 * Fonction déclenchée lors de la création d'une nouvelle actualité
 * Envoie automatiquement une notification
 */
exports.onNewsCreated = functions.firestore
  .document('news/{newsId}')
  .onCreate(async (snap, context) => {
    const newsData = snap.data();
    const newsId = context.params.newsId;

    // Vérifier si la notification automatique est activée
    if (!newsData.autoNotify || !newsData.isPublished) {
      return null;
    }

    try {
      // Créer une notification automatique
      const notificationData = {
        title: `Nouvelle actualité: ${newsData.title}`,
        body: newsData.excerpt || newsData.content?.substring(0, 100) + '...',
        type: 'news',
        targetType: 'groups',
        targetGroups: newsData.targetGroups || ['all'],
        linkType: 'news',
        linkId: newsId,
        priority: 'normal',
        createdBy: newsData.createdBy || 'system',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        status: 'sent',
        sentCount: 0,
        readCount: 0,
        clickCount: 0
      };

      await db.collection('notifications').add(notificationData);
      console.log(`Notification automatique créée pour l'actualité ${newsId}`);

    } catch (error) {
      console.error('Erreur lors de la création de la notification automatique:', error);
    }

    return null;
  });

// Instructions de déploiement:
// 1. Installer Firebase CLI: npm install -g firebase-tools
// 2. Initialiser le projet: firebase init functions
// 3. Copier ce code dans functions/index.js
// 4. Installer les dépendances: cd functions && npm install
// 5. Déployer: firebase deploy --only functions
