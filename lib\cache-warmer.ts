"use client"

import { optimizedMenuService } from "@/lib/optimized-menu-service"
import { getUserGroups } from "@/lib/user-utils"

class CacheWarmer {
  private static instance: CacheWarmer
  private isWarming = false
  private warmingPromise: Promise<void> | null = null

  static getInstance(): CacheWarmer {
    if (!CacheWarmer.instance) {
      CacheWarmer.instance = new CacheWarmer()
    }
    return CacheWarmer.instance
  }

  /**
   * Warm up caches for a user
   */
  async warmUserCache(userId: string): Promise<void> {
    if (this.isWarming) {
      return this.warmingPromise || Promise.resolve()
    }

    this.isWarming = true
    console.log(`🔥 Warming cache for user: ${userId}`)

    this.warmingPromise = this.performCacheWarming(userId)

    try {
      await this.warmingPromise
      console.log(`✅ Cache warming completed for user: ${userId}`)
    } catch (error) {
      console.error(`❌ Cache warming failed for user: ${userId}`, error)
    } finally {
      this.isWarming = false
      this.warmingPromise = null
    }
  }

  private async performCacheWarming(userId: string): Promise<void> {
    try {
      // Step 1: Warm user groups cache
      console.log("🔥 Warming user groups cache...")
      const userGroups = await getUserGroups(userId)

      // Step 2: Warm menu items cache
      console.log("🔥 Warming menu items cache...")
      try {
        await optimizedMenuService.preloadMenuItems(userGroups, userId)
      } catch (error) {
        console.warn("Erreur lors du préchargement des menu items, continuant...", error)
      }

      // Step 3: Preload critical images
      console.log("🔥 Preloading critical images...")
      await this.preloadCriticalImages()

      // Step 4: Warm other essential caches
      console.log("🔥 Warming other essential caches...")
      await this.warmOtherCaches(userId)

    } catch (error) {
      console.error("Error during cache warming:", error)
      throw error
    }
  }

  private async preloadCriticalImages(): Promise<void> {
    const criticalImages = [
      "/logo-acr-direct.png",
      "/acr-icon.png",
      "/placeholder.svg"
    ]

    const imagePromises = criticalImages.map(src => {
      return new Promise<void>((resolve, reject) => {
        const img = new Image()
        img.onload = () => resolve()
        img.onerror = () => {
          console.warn(`Failed to preload image: ${src}`)
          resolve() // Don't fail the whole process for one image
        }
        img.src = src
      })
    })

    await Promise.all(imagePromises)
  }

  private async warmOtherCaches(userId: string): Promise<void> {
    try {
      // Warm favorites cache
      const favoritesKey = `user_favorites_${userId}`
      if (!localStorage.getItem(favoritesKey)) {
        // Set empty favorites as default
        localStorage.setItem(favoritesKey, JSON.stringify([]))
        localStorage.setItem(`${favoritesKey}_timestamp`, Date.now().toString())
      }

      // Warm user preferences cache
      const preferencesKey = `user_preferences_${userId}`
      if (!localStorage.getItem(preferencesKey)) {
        // Set default preferences
        const defaultPreferences = {
          theme: 'system',
          notifications: true,
          language: 'fr'
        }
        localStorage.setItem(preferencesKey, JSON.stringify(defaultPreferences))
        localStorage.setItem(`${preferencesKey}_timestamp`, Date.now().toString())
      }

    } catch (error) {
      console.warn("Error warming other caches:", error)
    }
  }

  /**
   * Clear all caches for a user
   */
  clearUserCache(userId: string): void {
    console.log(`🧹 Clearing cache for user: ${userId}`)

    // Clear optimized menu service cache
    optimizedMenuService.clearCache(userId)

    // Clear localStorage caches
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.includes(userId)) {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key))

    console.log(`🧹 Cleared ${keysToRemove.length} cache entries for user: ${userId}`)
  }

  /**
   * Get cache statistics
   */
  getCacheStats(userId: string): {
    groupsCached: boolean
    menuCached: boolean
    favoritesCached: boolean
    preferencesCached: boolean
    totalCacheSize: number
  } {
    const groupsCached = !!localStorage.getItem(`user_groups_${userId}`)
    const menuCached = !!localStorage.getItem(`menu_cache_${userId}_admin`) // Check for any menu cache
    const favoritesCached = !!localStorage.getItem(`user_favorites_${userId}`)
    const preferencesCached = !!localStorage.getItem(`user_preferences_${userId}`)

    // Calculate total cache size (approximate)
    let totalCacheSize = 0
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.includes(userId)) {
        const value = localStorage.getItem(key)
        if (value) {
          totalCacheSize += value.length
        }
      }
    }

    return {
      groupsCached,
      menuCached,
      favoritesCached,
      preferencesCached,
      totalCacheSize
    }
  }
}

export const cacheWarmer = CacheWarmer.getInstance()
