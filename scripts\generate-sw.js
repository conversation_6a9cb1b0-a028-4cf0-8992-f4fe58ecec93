#!/usr/bin/env node

/**
 * <PERSON>ript to generate firebase-messaging-sw.js with real Firebase configuration
 * This script reads environment variables and injects them into the service worker
 */

const fs = require('fs');
const path = require('path');

// Environment variables are already loaded by Next.js

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Validate configuration
const missingVars = Object.entries(firebaseConfig)
  .filter(([key, value]) => !value)
  .map(([key]) => key);

if (missingVars.length > 0) {
  console.warn('⚠️  Missing Firebase environment variables:', missingVars);
  console.warn('Using placeholder values for service worker');
}

// Service worker template
const serviceWorkerTemplate = `// Firebase Cloud Messaging Service Worker
// This file handles background notifications and notification clicks
// Generated automatically with Firebase configuration

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js');

// Firebase configuration
const firebaseConfig = ${JSON.stringify(firebaseConfig, null, 2)};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);

  // Customize notification here
  const notificationTitle = payload.notification?.title || 'ACR Direct';
  const notificationOptions = {
    body: payload.notification?.body || 'Nouvelle notification',
    icon: '/android-chrome-192x192.png',
    badge: '/favicon-32x32.png',
    tag: 'acr-direct-notification',
    requireInteraction: payload.data?.priority === 'high',
    silent: payload.data?.priority === 'low',
    data: {
      url: payload.data?.url || '/dashboard/notifications',
      notificationId: payload.data?.notificationId,
      type: payload.data?.type || 'general'
    },
    actions: [
      {
        action: 'open',
        title: 'Ouvrir',
        icon: '/android-chrome-192x192.png'
      },
      {
        action: 'dismiss',
        title: 'Ignorer'
      }
    ]
  };

  // Add specific actions based on notification type
  if (payload.data?.type === 'news' && payload.data?.linkId) {
    notificationOptions.actions.unshift({
      action: 'read_news',
      title: 'Lire l\\'article'
    });
  } else if (payload.data?.type === 'page' && payload.data?.linkId) {
    notificationOptions.actions.unshift({
      action: 'view_page',
      title: 'Voir la page'
    });
  }

  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification click received.');

  event.notification.close();

  const action = event.action;
  const data = event.notification.data;

  let url = '/dashboard/notifications'; // Default URL

  // Determine URL based on action and data
  if (action === 'dismiss') {
    return; // Just close the notification
  } else if (action === 'read_news' && data.linkId) {
    url = \`/dashboard/news/\${data.linkId}\`;
  } else if (action === 'view_page' && data.linkId) {
    url = \`/dashboard/pages/\${data.linkId}\`;
  } else if (action === 'open' || !action) {
    url = data.url || '/dashboard/notifications';
  }

  // Open the URL
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url === url && 'focus' in client) {
          return client.focus();
        }
      }

      // If no existing window/tab, open a new one
      if (clients.openWindow) {
        return clients.openWindow(url);
      }
    })
  );

  // Track notification click analytics (if enabled)
  if (data.notificationId) {
    // Send analytics data to the main thread
    self.clients.matchAll().then(clients => {
      clients.forEach(client => {
        client.postMessage({
          type: 'NOTIFICATION_CLICKED',
          notificationId: data.notificationId,
          action: action || 'open'
        });
      });
    });
  }
});

// Handle notification close
self.addEventListener('notificationclose', (event) => {
  console.log('[firebase-messaging-sw.js] Notification closed.');

  const data = event.notification.data;

  // Track notification dismissal analytics (if enabled)
  if (data.notificationId) {
    self.clients.matchAll().then(clients => {
      clients.forEach(client => {
        client.postMessage({
          type: 'NOTIFICATION_DISMISSED',
          notificationId: data.notificationId
        });
      });
    });
  }
});

// Handle service worker activation
self.addEventListener('activate', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker activated.');

  // Clean up old caches if needed
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          // Clean up old notification-related caches
          if (cacheName.startsWith('notifications-') && cacheName !== 'notifications-v1') {
            console.log('[firebase-messaging-sw.js] Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Handle service worker installation
self.addEventListener('install', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker installed.');

  // Skip waiting to activate immediately
  self.skipWaiting();
});

// Handle messages from the main thread
self.addEventListener('message', (event) => {
  console.log('[firebase-messaging-sw.js] Message received:', event.data);

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Error handling
self.addEventListener('error', (event) => {
  console.error('[firebase-messaging-sw.js] Service worker error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('[firebase-messaging-sw.js] Unhandled promise rejection:', event.reason);
});

console.log('[firebase-messaging-sw.js] Firebase messaging service worker loaded successfully.');
`;

// Write the service worker file
const outputPath = path.join(process.cwd(), 'public', 'firebase-messaging-sw.js');

try {
  fs.writeFileSync(outputPath, serviceWorkerTemplate);
  console.log('✅ Firebase messaging service worker generated successfully');
  console.log('📁 Output:', outputPath);

  if (missingVars.length === 0) {
    console.log('🔧 Configuration: Using real Firebase environment variables');
  } else {
    console.log('⚠️  Configuration: Using placeholder values (missing env vars)');
  }
} catch (error) {
  console.error('❌ Error generating service worker:', error);
  process.exit(1);
}
