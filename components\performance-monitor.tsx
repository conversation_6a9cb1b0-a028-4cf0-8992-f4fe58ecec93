"use client"

import { useEffect, useState } from "react"
import { measureWebVitals } from "@/lib/performance"
import { useAuth } from "@/components/auth-provider"
import { useGroups } from "@/components/group-context"
import { useOptimizedMenu } from "@/hooks/use-optimized-menu"

interface PerformanceMetrics {
  authTime: number
  groupsTime: number
  menuTime: number
  totalTime: number
  cacheHits: number
  cacheMisses: number
}

export function PerformanceMonitor() {
  const [isActive, setIsActive] = useState(false)
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [startTime] = useState(Date.now())
  const { user } = useAuth()
  const { userGroups, isGroupsLoading } = useGroups()
  const { menuItems, isMenuLoading } = useOptimizedMenu()

  useEffect(() => {
    // Only activate in development or if explicitly enabled
    const shouldActivate =
      process.env.NODE_ENV === "development" || localStorage.getItem("enablePerfMonitoring") === "true"

    if (shouldActivate) {
      setIsActive(true)
      measureWebVitals()

      // Monitor notification system performance
      console.log("🔍 Performance monitoring activé - surveillance du système de notifications")

      // Track Firestore queries
      const originalQuery = window.query
      if (originalQuery) {
        window.query = (...args: any[]) => {
          const start = performance.now()
          const result = originalQuery(...args)
          const duration = performance.now() - start

          if (duration > 100) {
            console.warn(`🐌 Requête Firestore lente: ${duration.toFixed(2)}ms`, args)
          }

          return result
        }
      }
    }

    // Listen for keyboard shortcut to toggle monitoring
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+Shift+P to toggle performance monitoring
      if (e.ctrlKey && e.shiftKey && e.key === "P") {
        e.preventDefault()
        const newState = !isActive
        setIsActive(newState)
        localStorage.setItem("enablePerfMonitoring", newState.toString())

        if (newState) {
          measureWebVitals()
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [isActive])

  // Track performance metrics for group-based rendering
  useEffect(() => {
    if (!user || isGroupsLoading || isMenuLoading || !isActive) return

    const authTime = Date.now() - startTime
    const groupsTime = userGroups.length > 0 ? Date.now() - startTime : 0
    const menuTime = menuItems.length > 0 ? Date.now() - startTime : 0
    const totalTime = Date.now() - startTime

    // Count cache hits/misses from localStorage
    const cacheHits = parseInt(localStorage.getItem('perf_cache_hits') || '0')
    const cacheMisses = parseInt(localStorage.getItem('perf_cache_misses') || '0')

    const newMetrics = {
      authTime,
      groupsTime,
      menuTime,
      totalTime,
      cacheHits,
      cacheMisses
    }

    setMetrics(newMetrics)

    // Log performance metrics
    console.log('🚀 Group-based Rendering Performance:', {
      authTime: `${authTime}ms`,
      groupsTime: `${groupsTime}ms`,
      menuTime: `${menuTime}ms`,
      totalTime: `${totalTime}ms`,
      cacheHitRate: cacheHits + cacheMisses > 0 ? `${Math.round((cacheHits / (cacheHits + cacheMisses)) * 100)}%` : 'N/A',
      userGroups: userGroups.length,
      menuItems: menuItems.length
    })
  }, [user, userGroups, menuItems, isGroupsLoading, isMenuLoading, startTime, isActive])

  // Render performance overlay if active and metrics available
  if (isActive && metrics) {
    return (
      <div className="fixed bottom-4 right-4 bg-black/90 text-white p-3 rounded-lg text-xs font-mono z-50 max-w-xs">
        <div className="font-bold mb-2 text-green-400">⚡ Performance</div>
        <div>Auth: {metrics.authTime}ms</div>
        <div>Groups: {metrics.groupsTime}ms</div>
        <div>Menu: {metrics.menuTime}ms</div>
        <div className="font-semibold">Total: {metrics.totalTime}ms</div>
        <div>Cache: {metrics.cacheHits}H/{metrics.cacheMisses}M</div>
        <div className="text-xs text-gray-400 mt-2">Ctrl+Shift+P to toggle</div>
      </div>
    )
  }

  return null
}
