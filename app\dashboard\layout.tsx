import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { DashboardNav } from "@/components/dashboard-nav"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardPreloader } from "@/components/dashboard-preloader"
import { PerformanceMonitor } from "@/components/performance-monitor"

export const metadata: Metadata = {
  title: "Dashboard | ACR Direct",
  description: "Votre espace client personnalisé",
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <DashboardPreloader>
      <div className="flex min-h-screen flex-col bg-background">
        <DashboardHeader />
        <div className="container grid flex-1 gap-12 md:grid-cols-[240px_1fr] px-0 sm:px-4 md:px-6">
          <aside className="hidden w-[240px] flex-col md:flex">
            <DashboardNav />
          </aside>
          <main
            className="flex w-full flex-1 flex-col overflow-hidden px-4 sm:px-6 md:px-8 py-4 md:py-6"
            id="main-content"
          >
            {children}
          </main>
        </div>
      </div>
      <PerformanceMonitor />
    </DashboardPreloader>
  )
}
