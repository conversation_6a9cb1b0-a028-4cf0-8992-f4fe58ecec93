"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Bell, BellRing } from "lucide-react"
import { cn } from "@/lib/utils"
import { useAuth } from "@/lib/hooks/use-auth"
import { collection, query, where, onSnapshot, limit } from "firebase/firestore"
import { db } from "@/lib/firebase"

interface HeaderNotificationButtonProps {
  className?: string
  onClick?: () => void
}

/**
 * Composant léger pour l'en-tête qui ne charge que le compteur
 * sans impacter les performances du dashboard principal
 */
export function HeaderNotificationButton({ 
  className, 
  onClick 
}: HeaderNotificationButtonProps) {
  const { user } = useAuth()
  const [unreadCount, setUnreadCount] = useState(0)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    if (!user?.uid) {
      setUnreadCount(0)
      setIsLoaded(true)
      return
    }

    // <PERSON><PERSON><PERSON> pour ne pas interférer avec le chargement principal
    const timeoutId = setTimeout(() => {
      try {
        const notificationsQuery = query(
          collection(db(), "userNotifications"),
          where("userId", "==", user.uid),
          where("isRead", "==", false),
          limit(10) // Limite très faible pour optimiser
        )

        const unsubscribe = onSnapshot(
          notificationsQuery,
          (snapshot) => {
            setUnreadCount(snapshot.size)
            setIsLoaded(true)
          },
          (error) => {
            console.error("Erreur compteur notifications:", error)
            setUnreadCount(0)
            setIsLoaded(true)
          }
        )

        return () => unsubscribe()
      } catch (error) {
        console.error("Erreur initialisation compteur notifications:", error)
        setUnreadCount(0)
        setIsLoaded(true)
      }
    }, 3000) // Délai de 3 secondes pour laisser le dashboard se charger

    return () => {
      clearTimeout(timeoutId)
    }
  }, [user?.uid])

  return (
    <Button 
      variant="ghost" 
      size="icon" 
      className={cn("relative", className)}
      onClick={onClick}
    >
      {isLoaded && unreadCount > 0 ? (
        <BellRing className="h-5 w-5" />
      ) : (
        <Bell className="h-5 w-5" />
      )}
      {isLoaded && unreadCount > 0 && (
        <Badge 
          variant="destructive" 
          className="absolute -top-1 -right-1 h-4 w-4 flex items-center justify-center p-0 text-xs"
        >
          {unreadCount > 9 ? "9+" : unreadCount}
        </Badge>
      )}
    </Button>
  )
}
