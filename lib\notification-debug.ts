/**
 * Utilitaires de débogage pour le système de notifications
 */

import { db } from "@/lib/firebase"
import { collection, getDocs, doc, getDoc, query, where, orderBy, limit } from "firebase/firestore"

export interface NotificationDebugInfo {
  userTokensCount: number
  notificationsCount: number
  analyticsEnabled: boolean
  analyticsUsage: number
  analyticsLimit: number
  lastError?: string
  recommendations: string[]
}

/**
 * Collecte des informations de débogage sur le système de notifications
 */
export async function getNotificationDebugInfo(): Promise<NotificationDebugInfo> {
  const recommendations: string[] = []
  let userTokensCount = 0
  let notificationsCount = 0
  let analyticsEnabled = false
  let analyticsUsage = 0
  let analyticsLimit = 45000
  let lastError: string | undefined

  try {
    // Vérifier les tokens utilisateurs
    try {
      const tokensSnapshot = await getDocs(collection(db(), "userTokens"))
      userTokensCount = tokensSnapshot.size
      
      if (userTokensCount === 0) {
        recommendations.push("Aucun utilisateur n'a accepté les notifications. Testez en acceptant les notifications dans votre navigateur.")
      }
    } catch (error) {
      recommendations.push("Erreur lors de la récupération des tokens utilisateurs")
      lastError = `Tokens: ${error}`
    }

    // Vérifier les notifications
    try {
      const notificationsSnapshot = await getDocs(query(collection(db(), "notifications"), limit(1)))
      notificationsCount = notificationsSnapshot.size
    } catch (error) {
      recommendations.push("Erreur lors de la récupération des notifications")
      lastError = `Notifications: ${error}`
    }

    // Vérifier les paramètres analytics
    try {
      const analyticsRef = doc(db(), "settings", "notificationAnalytics")
      const analyticsSnapshot = await getDoc(analyticsRef)
      
      if (analyticsSnapshot.exists()) {
        const data = analyticsSnapshot.data()
        analyticsEnabled = data.enabled || false
        analyticsUsage = data.currentUsage || 0
        analyticsLimit = data.dailyLimit || 45000
        
        if (!analyticsEnabled) {
          recommendations.push("Les analytics sont désactivées. Cela peut être dû à l'atteinte de la limite quotidienne.")
        }
        
        if (analyticsUsage > analyticsLimit * 0.8) {
          recommendations.push("Usage Firebase proche de la limite quotidienne.")
        }
      } else {
        recommendations.push("Document notificationAnalytics manquant. Exécutez le script d'initialisation.")
        lastError = "Document notificationAnalytics introuvable"
      }
    } catch (error) {
      recommendations.push("Erreur lors de la vérification des analytics")
      lastError = `Analytics: ${error}`
    }

    // Vérifications générales
    if (typeof window !== 'undefined') {
      if (!('Notification' in window)) {
        recommendations.push("Les notifications ne sont pas supportées par ce navigateur")
      } else if (Notification.permission === 'denied') {
        recommendations.push("Les notifications sont bloquées dans ce navigateur")
      } else if (Notification.permission === 'default') {
        recommendations.push("Les notifications n'ont pas encore été autorisées")
      }

      if (!('serviceWorker' in navigator)) {
        recommendations.push("Les service workers ne sont pas supportés")
      }
    }

    if (recommendations.length === 0) {
      recommendations.push("Système de notifications configuré correctement")
    }

  } catch (error) {
    recommendations.push("Erreur générale lors du diagnostic")
    lastError = `Général: ${error}`
  }

  return {
    userTokensCount,
    notificationsCount,
    analyticsEnabled,
    analyticsUsage,
    analyticsLimit,
    lastError,
    recommendations
  }
}

/**
 * Affiche les informations de débogage dans la console
 */
export async function logNotificationDebugInfo(): Promise<void> {
  console.group("🔔 Diagnostic du système de notifications")
  
  try {
    const debugInfo = await getNotificationDebugInfo()
    
    console.log("📊 Statistiques:")
    console.log(`  - Utilisateurs avec tokens: ${debugInfo.userTokensCount}`)
    console.log(`  - Notifications créées: ${debugInfo.notificationsCount}`)
    console.log(`  - Analytics activées: ${debugInfo.analyticsEnabled}`)
    console.log(`  - Usage Firebase: ${debugInfo.analyticsUsage}/${debugInfo.analyticsLimit}`)
    
    if (debugInfo.lastError) {
      console.error("❌ Dernière erreur:", debugInfo.lastError)
    }
    
    console.log("💡 Recommandations:")
    debugInfo.recommendations.forEach(rec => console.log(`  - ${rec}`))
    
  } catch (error) {
    console.error("Erreur lors du diagnostic:", error)
  }
  
  console.groupEnd()
}

/**
 * Fonction utilitaire pour tester les notifications
 */
export async function testNotificationSystem(): Promise<boolean> {
  console.log("🧪 Test du système de notifications...")
  
  try {
    // Vérifier les permissions
    if (typeof window === 'undefined') {
      console.error("Test impossible côté serveur")
      return false
    }
    
    if (!('Notification' in window)) {
      console.error("Notifications non supportées")
      return false
    }
    
    if (Notification.permission !== 'granted') {
      console.warn("Permission de notification non accordée")
      return false
    }
    
    // Tester une notification locale
    new Notification("Test ACR Direct", {
      body: "Le système de notifications fonctionne correctement!",
      icon: "/icon-192x192.png"
    })
    
    console.log("✅ Test réussi - notification affichée")
    return true
    
  } catch (error) {
    console.error("❌ Test échoué:", error)
    return false
  }
}

// Exposer les fonctions de debug en mode développement
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).notificationDebug = {
    getInfo: getNotificationDebugInfo,
    log: logNotificationDebugInfo,
    test: testNotificationSystem
  }
  
  console.log("🔧 Fonctions de debug disponibles:")
  console.log("  - window.notificationDebug.log() - Afficher le diagnostic")
  console.log("  - window.notificationDebug.test() - Tester les notifications")
  console.log("  - window.notificationDebug.getInfo() - Obtenir les infos")
}
