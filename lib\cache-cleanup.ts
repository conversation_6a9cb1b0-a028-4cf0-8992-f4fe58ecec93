"use client"

/**
 * Utility to clean up corrupted cache data
 */
export class CacheCleanup {
  /**
   * Clean up corrupted user groups cache
   */
  static cleanupUserGroupsCache(userId: string): void {
    try {
      const cachedGroups = localStorage.getItem(`user_groups_${userId}`)
      if (cachedGroups) {
        try {
          const groups = JSON.parse(cachedGroups)
          
          // Check if groups is valid
          if (!Array.isArray(groups) || groups.some(g => typeof g !== 'string')) {
            console.warn("Cache des groupes corrompu détecté, nettoyage...")
            localStorage.removeItem(`user_groups_${userId}`)
            localStorage.removeItem(`user_groups_timestamp_${userId}`)
            localStorage.removeItem(`user_permission_update_${userId}`)
          }
        } catch (error) {
          console.warn("Erreur lors de la validation du cache des groupes, nettoyage...")
          localStorage.removeItem(`user_groups_${userId}`)
          localStorage.removeItem(`user_groups_timestamp_${userId}`)
          localStorage.removeItem(`user_permission_update_${userId}`)
        }
      }
    } catch (error) {
      console.error("Erreur lors du nettoyage du cache des groupes:", error)
    }
  }

  /**
   * Clean up all corrupted cache for a user
   */
  static cleanupAllUserCache(userId: string): void {
    try {
      // Clean groups cache
      this.cleanupUserGroupsCache(userId)
      
      // Clean menu cache
      const menuCacheKeys = Object.keys(localStorage).filter(key => 
        key.startsWith('menu_cache_') && key.includes(userId)
      )
      
      menuCacheKeys.forEach(key => {
        try {
          const cached = localStorage.getItem(key)
          if (cached) {
            JSON.parse(cached) // Test if valid JSON
          }
        } catch (error) {
          console.warn(`Cache menu corrompu détecté (${key}), suppression...`)
          localStorage.removeItem(key)
        }
      })
      
      console.log(`Nettoyage du cache terminé pour l'utilisateur: ${userId}`)
    } catch (error) {
      console.error("Erreur lors du nettoyage complet du cache:", error)
    }
  }

  /**
   * Clean up all corrupted cache in localStorage
   */
  static cleanupAllCorruptedCache(): void {
    try {
      const keysToCheck = Object.keys(localStorage).filter(key => 
        key.startsWith('user_groups_') || 
        key.startsWith('menu_cache_') ||
        key.startsWith('user_favorites_') ||
        key.startsWith('user_preferences_')
      )
      
      let cleanedCount = 0
      
      keysToCheck.forEach(key => {
        try {
          const value = localStorage.getItem(key)
          if (value) {
            JSON.parse(value) // Test if valid JSON
          }
        } catch (error) {
          console.warn(`Cache corrompu détecté (${key}), suppression...`)
          localStorage.removeItem(key)
          cleanedCount++
        }
      })
      
      if (cleanedCount > 0) {
        console.log(`Nettoyage terminé: ${cleanedCount} entrées de cache corrompues supprimées`)
      }
    } catch (error) {
      console.error("Erreur lors du nettoyage global du cache:", error)
    }
  }

  /**
   * Validate and fix user groups format
   */
  static validateAndFixUserGroups(groups: any): string[] {
    try {
      if (!Array.isArray(groups)) {
        return []
      }
      
      return groups.filter(group => {
        if (typeof group === 'string') {
          return true
        }
        
        // Handle case where groups might be JSON strings
        if (typeof group === 'string' && group.startsWith('[') && group.endsWith(']')) {
          try {
            const parsed = JSON.parse(group)
            return Array.isArray(parsed) && parsed.every(g => typeof g === 'string')
          } catch {
            return false
          }
        }
        
        return false
      })
    } catch (error) {
      console.error("Erreur lors de la validation des groupes:", error)
      return []
    }
  }
}

// Auto-cleanup on module load
if (typeof window !== 'undefined') {
  // Run cleanup after a short delay to avoid blocking initial load
  setTimeout(() => {
    CacheCleanup.cleanupAllCorruptedCache()
  }, 1000)
}
