"use client"

import { collection, getDocs, query, where, orderBy } from "firebase/firestore"
import { db } from "@/lib/firebase"

interface MenuItem {
  id: string
  title: string
  path: string
  iconUrl: string | null
  displayOrder: number
  targetGroups: string[]
  isPublished: boolean
}

interface CachedMenuData {
  items: MenuItem[]
  timestamp: number
  version: string
}

class OptimizedMenuService {
  private static instance: OptimizedMenuService
  private menuCache = new Map<string, CachedMenuData>()
  private loadingPromises = new Map<string, Promise<MenuItem[]>>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
  private readonly CACHE_VERSION = "v2.0"

  static getInstance(): OptimizedMenuService {
    if (!OptimizedMenuService.instance) {
      OptimizedMenuService.instance = new OptimizedMenuService()
    }
    return OptimizedMenuService.instance
  }

  /**
   * Get menu items for user groups with aggressive caching
   */
  async getMenuItemsForGroups(userGroups: string[], userId: string): Promise<MenuItem[]> {
    const cacheKey = this.getCacheKey(userGroups, userId)

    // Check memory cache first
    const memoryCache = this.menuCache.get(cacheKey)
    if (memoryCache && this.isCacheValid(memoryCache)) {
      console.log("Menu items trouvés en cache mémoire")
      return memoryCache.items
    }

    // Check localStorage cache
    const localCache = this.getLocalCache(cacheKey)
    if (localCache && this.isCacheValid(localCache)) {
      console.log("Menu items trouvés en cache local")
      this.menuCache.set(cacheKey, localCache)
      return localCache.items
    }

    // Check if already loading to prevent duplicate requests
    const existingPromise = this.loadingPromises.get(cacheKey)
    if (existingPromise) {
      console.log("Attente de la requête en cours...")
      return existingPromise
    }

    // Load from Firestore
    const loadingPromise = this.loadMenuItemsFromFirestore(userGroups)
    this.loadingPromises.set(cacheKey, loadingPromise)

    try {
      const items = await loadingPromise

      // Cache the results
      const cacheData: CachedMenuData = {
        items,
        timestamp: Date.now(),
        version: this.CACHE_VERSION
      }

      this.menuCache.set(cacheKey, cacheData)
      this.setLocalCache(cacheKey, cacheData)

      console.log(`${items.length} éléments de menu chargés et mis en cache`)
      return items
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }

  /**
   * Preload menu items for better performance
   */
  async preloadMenuItems(userGroups: string[], userId: string): Promise<void> {
    try {
      await this.getMenuItemsForGroups(userGroups, userId)
      console.log("Menu items préchargés avec succès")
    } catch (error) {
      console.error("Erreur lors du préchargement des menu items:", error)
    }
  }

  /**
   * Clear cache for specific user or all cache
   */
  clearCache(userId?: string): void {
    if (userId) {
      // Clear cache for specific user
      const keysToDelete = Array.from(this.menuCache.keys()).filter(key => key.includes(userId))
      keysToDelete.forEach(key => {
        this.menuCache.delete(key)
        localStorage.removeItem(`menu_cache_${key}`)
      })
    } else {
      // Clear all cache
      this.menuCache.clear()
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('menu_cache_')) {
          localStorage.removeItem(key)
        }
      })
    }
    console.log("Cache des menu items effacé")
  }

  /**
   * Load menu items from Firestore with optimized query
   */
  private async loadMenuItemsFromFirestore(userGroups: string[]): Promise<MenuItem[]> {
    try {
      console.log(`Chargement des éléments de menu depuis Firestore pour les groupes: ${userGroups.join(", ")}`)

      // Simple query without ordering to avoid index requirement
      const menuItemsRef = collection(db(), "menuItems")
      const menuItemsQuery = query(
        menuItemsRef,
        where("isPublished", "==", true)
      )

      const snapshot = await getDocs(menuItemsQuery)
      const items: MenuItem[] = []

      snapshot.forEach((doc) => {
        const data = doc.data()

        // Check if item is for user's groups
        const isForUser = data.targetGroups?.includes("all") ||
          (Array.isArray(data.targetGroups) &&
           data.targetGroups.some((group: string) => userGroups.includes(group)))

        if (isForUser) {
          items.push({
            id: doc.id,
            title: data.title,
            path: `/dashboard/pages/${data.slug}`,
            iconUrl: data.iconUrl || null,
            displayOrder: data.displayOrder !== undefined ? data.displayOrder : 0,
            targetGroups: data.targetGroups || [],
            isPublished: data.isPublished
          })
        }
      })

      // Sort by display order
      items.sort((a, b) => a.displayOrder - b.displayOrder)

      console.log(`${items.length} éléments de menu chargés et triés`)
      return items
    } catch (error) {
      console.error("Erreur lors du chargement des menu items depuis Firestore:", error)

      // Return empty array instead of throwing to prevent app crash
      if (error instanceof Error && error.message.includes('quota-exceeded')) {
        console.warn("Quota Firebase dépassé, retour d'un menu vide")
        return []
      }

      // For index errors, also return empty array and log the solution
      if (error instanceof Error && error.message.includes('requires an index')) {
        console.warn("Index Firestore manquant pour la requête optimisée, retour d'un menu vide")
        return []
      }

      throw error
    }
  }

  private getCacheKey(userGroups: string[], userId: string): string {
    return `${userId}_${userGroups.sort().join(",")}`
  }

  private isCacheValid(cacheData: CachedMenuData): boolean {
    const age = Date.now() - cacheData.timestamp
    return age < this.CACHE_DURATION && cacheData.version === this.CACHE_VERSION
  }

  private getLocalCache(cacheKey: string): CachedMenuData | null {
    try {
      const cached = localStorage.getItem(`menu_cache_${cacheKey}`)
      return cached ? JSON.parse(cached) : null
    } catch {
      return null
    }
  }

  private setLocalCache(cacheKey: string, data: CachedMenuData): void {
    try {
      localStorage.setItem(`menu_cache_${cacheKey}`, JSON.stringify(data))
    } catch (error) {
      console.warn("Impossible de sauvegarder le cache des menu items:", error)
    }
  }
}

export const optimizedMenuService = OptimizedMenuService.getInstance()
