"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Users,
  Search,
  Smartphone,
  Monitor,
  Tablet,
  RefreshCw,
  Bell,
  BellOff,
  Trash2,
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import { collection, getDocs, query, where, deleteDoc, doc, orderBy } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { toast } from "sonner"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"

interface UserToken {
  id: string
  userId: string
  fcmToken: string
  platform: string
  userAgent: string
  createdAt: any
  updatedAt: any
  user?: {
    email: string
    displayName?: string
  }
}

interface NotificationUsersProps {
  className?: string
}

export function NotificationUsers({ className }: NotificationUsersProps) {
  const [tokens, setTokens] = useState<UserToken[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [deleting, setDeleting] = useState<string | null>(null)

  // Charger les tokens utilisateurs
  const loadUserTokens = async () => {
    try {
      setLoading(true)

      // Récupérer les tokens FCM avec gestion d'erreur améliorée
      let tokensSnapshot
      try {
        const tokensQuery = query(
          collection(db(), "userTokens"),
          orderBy("updatedAt", "desc")
        )
        tokensSnapshot = await getDocs(tokensQuery)
      } catch (tokensError) {
        console.warn("Erreur lors de la récupération des tokens, tentative sans tri:", tokensError)
        // Fallback sans orderBy si l'index n'existe pas
        const tokensQuery = query(collection(db(), "userTokens"))
        tokensSnapshot = await getDocs(tokensQuery)
      }

      // Récupérer les informations utilisateurs
      const usersQuery = query(collection(db(), "users"))
      const usersSnapshot = await getDocs(usersQuery)
      const usersMap = new Map()
      usersSnapshot.docs.forEach(doc => {
        usersMap.set(doc.id, {
          email: doc.data().email,
          displayName: doc.data().displayName
        })
      })

      const tokensData = tokensSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        user: usersMap.get(doc.data().userId)
      })) as UserToken[]

      // Trier côté client si nécessaire
      tokensData.sort((a, b) => {
        const aTime = a.updatedAt?.toDate?.() || new Date(0)
        const bTime = b.updatedAt?.toDate?.() || new Date(0)
        return bTime.getTime() - aTime.getTime()
      })

      setTokens(tokensData)
    } catch (error) {
      console.error("Erreur lors du chargement des tokens:", error)
      toast.error("Impossible de charger les tokens utilisateurs")
    } finally {
      setLoading(false)
    }
  }

  // Supprimer un token
  const deleteToken = async (tokenId: string) => {
    try {
      setDeleting(tokenId)
      await deleteDoc(doc(db(), "userTokens", tokenId))
      toast.success("Token supprimé avec succès")
      await loadUserTokens()
    } catch (error) {
      console.error("Erreur lors de la suppression:", error)
      toast.error("Impossible de supprimer le token")
    } finally {
      setDeleting(null)
    }
  }

  // Filtrer les tokens
  const filteredTokens = tokens.filter(token => {
    const searchLower = searchTerm.toLowerCase()
    return (
      token.user?.email?.toLowerCase().includes(searchLower) ||
      token.user?.displayName?.toLowerCase().includes(searchLower) ||
      token.platform.toLowerCase().includes(searchLower)
    )
  })

  // Obtenir l'icône de la plateforme
  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'android':
      case 'ios':
        return <Smartphone className="h-4 w-4" />
      case 'web':
      case 'windows':
      case 'macos':
      case 'linux':
        return <Monitor className="h-4 w-4" />
      default:
        return <Tablet className="h-4 w-4" />
    }
  }

  // Obtenir la couleur du badge de plateforme
  const getPlatformBadgeVariant = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'android':
        return 'default'
      case 'ios':
        return 'secondary'
      case 'web':
        return 'outline'
      default:
        return 'outline'
    }
  }

  useEffect(() => {
    loadUserTokens()
  }, [])

  // Statistiques
  const totalUsers = new Set(tokens.map(t => t.userId)).size
  const totalTokens = tokens.length
  const platformStats = tokens.reduce((acc, token) => {
    acc[token.platform] = (acc[token.platform] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs avec notifications</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              Utilisateurs uniques avec tokens FCM
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tokens actifs</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTokens}</div>
            <p className="text-xs text-muted-foreground">
              Appareils enregistrés pour les notifications
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Plateforme principale</CardTitle>
            {getPlatformIcon(Object.keys(platformStats).sort((a, b) => platformStats[b] - platformStats[a])[0] || 'web')}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold capitalize">
              {Object.keys(platformStats).sort((a, b) => platformStats[b] - platformStats[a])[0] || 'Aucune'}
            </div>
            <p className="text-xs text-muted-foreground">
              {platformStats[Object.keys(platformStats).sort((a, b) => platformStats[b] - platformStats[a])[0]] || 0} appareils
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Gestion des tokens */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Tokens FCM des utilisateurs</CardTitle>
              <CardDescription>
                Gérez les tokens de notification des utilisateurs et leurs appareils
              </CardDescription>
            </div>
            <Button
              variant="outline"
              onClick={loadUserTokens}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Actualiser
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Recherche */}
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher par email, nom ou plateforme..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {/* Alerte si aucun token */}
          {tokens.length === 0 ? (
            <Alert>
              <BellOff className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p>Aucun utilisateur n'a encore accepté les notifications.</p>
                  <p className="text-sm text-muted-foreground">
                    Les utilisateurs apparaîtront ici après avoir accepté les notifications push dans leur navigateur.
                    Pour tester, visitez l'application et acceptez les notifications quand elles sont proposées.
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          ) : filteredTokens.length === 0 ? (
            <Alert>
              <Search className="h-4 w-4" />
              <AlertDescription>
                Aucun résultat trouvé pour "{searchTerm}".
              </AlertDescription>
            </Alert>
          ) : (
            /* Table des tokens */
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Utilisateur</TableHead>
                    <TableHead>Plateforme</TableHead>
                    <TableHead>Dernière activité</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead className="w-[70px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTokens.map((token) => (
                    <TableRow key={token.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">
                            {token.user?.displayName || token.user?.email || 'Utilisateur inconnu'}
                          </p>
                          {token.user?.displayName && (
                            <p className="text-sm text-muted-foreground">
                              {token.user.email}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getPlatformIcon(token.platform)}
                          <Badge variant={getPlatformBadgeVariant(token.platform)}>
                            {token.platform}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {token.updatedAt ?
                            formatDistanceToNow(token.updatedAt.toDate(), {
                              addSuffix: true,
                              locale: fr
                            }) :
                            'Inconnue'
                          }
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm">Actif</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => deleteToken(token.id)}
                          disabled={deleting === token.id}
                          className="h-8 w-8"
                        >
                          {deleting === token.id ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
