"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Star } from "lucide-react"
import { Button } from "@/components/ui/button"
import { toggleFavorite, isInFavorites } from "@/lib/favorites-utils"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"

interface FavoriteButtonProps {
  newsId: string
  userId: string
  size?: "xs" | "sm" | "md" | "lg"
  className?: string
  showOnlyWhenFavorited?: boolean // New prop to control visibility
}

export function FavoriteButton({ newsId, userId, size = "md", className = "", showOnlyWhenFavorited = false }: FavoriteButtonProps) {
  const [isFavorite, setIsFavorite] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    const checkFavoriteStatus = async () => {
      try {
        if (!userId || !newsId) {
          setIsLoading(false)
          return
        }

        const status = await isInFavorites(userId, newsId)
        setIsFavorite(status)
      } catch (error) {
        console.error("Error checking favorite status:", error)
      } finally {
        setIsLoading(false)
      }
    }

    checkFavoriteStatus()
  }, [userId, newsId])

  const handleToggleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault() // Empêcher la navigation si le bouton est dans un lien
    e.stopPropagation() // Empêcher la propagation de l'événement

    if (!userId) return

    setIsLoading(true)
    try {
      const newStatus = await toggleFavorite(userId, newsId)
      setIsFavorite(newStatus)

      toast({
        title: newStatus ? "Ajouté aux favoris" : "Retiré des favoris",
        description: newStatus ? "L'article a été ajouté à vos favoris" : "L'article a été retiré de vos favoris",
        duration: 2000,
      })

      // Real-time updates are now handled automatically by the favorites utilities
    } catch (error) {
      console.error("Error toggling favorite:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la modification des favoris",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Définir les tailles en fonction du paramètre size
  const sizeClasses = {
    xs: "h-6 w-6",
    sm: "h-7 w-7",
    md: "h-9 w-9",
    lg: "h-10 w-10",
  }

  const iconSizes = {
    xs: "h-3 w-3",
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6",
  }

  // If showOnlyWhenFavorited is true and the item is not favorited, don't render anything
  if (showOnlyWhenFavorited && !isFavorite && !isLoading) {
    return null
  }

  return (
    <Button
      variant="link"
      size="icon"
      disabled={isLoading || !userId}
      onClick={handleToggleFavorite}
      className={`${sizeClasses[size]} ${className} ${
        isFavorite
          ? "text-yellow-500 hover:text-yellow-600 dark:text-yellow-400 dark:hover:text-yellow-300"
          : "text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
      }`}
      aria-label={isFavorite ? "Retirer des favoris" : "Ajouter aux favoris"}
    >
      <Star className={`${iconSizes[size]} ${isFavorite ? "fill-yellow-500 dark:fill-yellow-400" : "fill-none"}`} />
    </Button>
  )
}
