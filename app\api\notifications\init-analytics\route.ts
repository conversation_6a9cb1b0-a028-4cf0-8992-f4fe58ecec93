import { NextRequest, NextResponse } from "next/server"
import { initializeFirebaseAdmin } from "@/lib/server-auth"
import { doc, setDoc, serverTimestamp } from "firebase/firestore"

/**
 * API endpoint pour initialiser le document notificationAnalytics manquant
 * POST /api/notifications/init-analytics
 */
export async function POST(request: NextRequest) {
  try {
    // Initialiser Firebase Admin
    const { db, auth } = await initializeFirebaseAdmin()
    
    // Vérifier l'authentification (optionnel - peut être retiré pour le debug)
    const authHeader = request.headers.get('authorization')
    if (authHeader) {
      const token = authHeader.replace('Bearer ', '')
      try {
        await auth.verifyIdToken(token)
      } catch (authError) {
        console.warn("Token invalide, mais on continue pour l'initialisation")
      }
    }

    // Créer le document notificationAnalytics
    const settingsRef = doc(db, "settings", "notificationAnalytics")
    
    const defaultSettings = {
      enabled: true,
      dailyLimit: 45000, // 90% de la limite Firebase (50k)
      currentUsage: 0,
      lastReset: new Date().toISOString().split('T')[0],
      autoDisableOnLimit: true,
      priorityMode: 'normal',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }

    await setDoc(settingsRef, defaultSettings, { merge: true })

    return NextResponse.json({
      success: true,
      message: "Document notificationAnalytics initialisé avec succès",
      settings: {
        ...defaultSettings,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error("Erreur lors de l'initialisation des analytics:", error)
    
    return NextResponse.json({
      success: false,
      error: "Erreur lors de l'initialisation",
      details: error instanceof Error ? error.message : "Erreur inconnue"
    }, { status: 500 })
  }
}

/**
 * GET endpoint pour vérifier l'état du document
 */
export async function GET() {
  try {
    const { db } = await initializeFirebaseAdmin()
    
    const settingsRef = doc(db, "settings", "notificationAnalytics")
    const snapshot = await settingsRef.get()
    
    if (snapshot.exists) {
      return NextResponse.json({
        exists: true,
        data: snapshot.data()
      })
    } else {
      return NextResponse.json({
        exists: false,
        message: "Document notificationAnalytics n'existe pas"
      })
    }
    
  } catch (error) {
    console.error("Erreur lors de la vérification:", error)
    
    return NextResponse.json({
      error: "Erreur lors de la vérification",
      details: error instanceof Error ? error.message : "Erreur inconnue"
    }, { status: 500 })
  }
}
