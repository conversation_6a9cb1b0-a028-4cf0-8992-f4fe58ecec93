"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import {
  TestTube,
  Send,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Bell,
  Users,
  Database,
  Zap
} from "lucide-react"
import { NotificationService } from "@/lib/notification-service"
import { NotificationAnalyticsService } from "@/lib/notification-analytics"
import { useAuth } from "@/lib/hooks/use-auth"
import { toast } from "sonner"

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message: string
  duration?: number
}

interface NotificationTestProps {
  className?: string
}

export function NotificationTest({ className }: NotificationTestProps) {
  const { user } = useAuth()
  const [testing, setTesting] = useState(false)
  const [results, setResults] = useState<TestResult[]>([])

  // Tests à exécuter
  const runTests = async () => {
    if (!user?.uid) {
      toast.error("Vous devez être connecté pour exécuter les tests")
      return
    }

    setTesting(true)
    setResults([])

    const tests: TestResult[] = [
      { name: "Vérification du service worker FCM", status: 'pending', message: 'En cours...' },
      { name: "Vérification des paramètres analytics", status: 'pending', message: 'En cours...' },
      { name: "Test de création de notification", status: 'pending', message: 'En cours...' },
      { name: "Test de ciblage 'tous les utilisateurs'", status: 'pending', message: 'En cours...' },
      { name: "Test de ciblage par groupes", status: 'pending', message: 'En cours...' },
      { name: "Test des analytics", status: 'pending', message: 'En cours...' },
      { name: "Test de l'API d'envoi", status: 'pending', message: 'En cours...' },
      { name: "Vérification des limites Firebase", status: 'pending', message: 'En cours...' }
    ]

    setResults([...tests])

    // Test 1: Vérification du service worker FCM
    try {
      const start = performance.now()

      if ('serviceWorker' in navigator) {
        // Vérifier si le service worker est enregistré
        const registrations = await navigator.serviceWorker.getRegistrations()
        const fcmRegistration = registrations.find(reg =>
          reg.scope.includes('firebase-cloud-messaging-push-scope') ||
          reg.active?.scriptURL.includes('firebase-messaging-sw')
        )

        if (fcmRegistration) {
          const duration = performance.now() - start
          tests[0] = {
            name: "Vérification du service worker FCM",
            status: 'success',
            message: `Service worker FCM enregistré et actif`,
            duration
          }
        } else {
          // Tenter d'enregistrer le service worker
          try {
            const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
              scope: '/firebase-cloud-messaging-push-scope'
            })
            const duration = performance.now() - start
            tests[0] = {
              name: "Vérification du service worker FCM",
              status: 'success',
              message: `Service worker FCM enregistré avec succès`,
              duration
            }
          } catch (swError) {
            tests[0] = {
              name: "Vérification du service worker FCM",
              status: 'error',
              message: `Impossible d'enregistrer le service worker: ${swError instanceof Error ? swError.message : 'Erreur inconnue'}`
            }
          }
        }
      } else {
        tests[0] = {
          name: "Vérification du service worker FCM",
          status: 'error',
          message: "Service workers non supportés par ce navigateur"
        }
      }
    } catch (error) {
      tests[0] = {
        name: "Vérification du service worker FCM",
        status: 'error',
        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      }
    }
    setResults([...tests])

    // Test 2: Vérification des paramètres analytics
    try {
      const start = performance.now()
      const settings = await NotificationAnalyticsService.getAnalyticsSettings()
      const duration = performance.now() - start

      tests[1] = {
        name: "Vérification des paramètres analytics",
        status: 'success',
        message: `Paramètres chargés (${settings.enabled ? 'activés' : 'désactivés'})`,
        duration
      }
    } catch (error) {
      tests[1] = {
        name: "Vérification des paramètres analytics",
        status: 'error',
        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      }
    }
    setResults([...tests])

    // Test 3: Test de création de notification
    try {
      const start = performance.now()
      const notificationId = await NotificationService.createNotification({
        title: "Test de notification",
        body: "Ceci est un test automatique du système de notifications",
        type: "system",
        targetType: "individual",
        targetUsers: [user.uid],
        priority: "normal",
        createdBy: user.uid,
        status: "draft"
      })
      const duration = performance.now() - start

      tests[2] = {
        name: "Test de création de notification",
        status: 'success',
        message: `Notification créée avec l'ID: ${notificationId}`,
        duration
      }
    } catch (error) {
      tests[2] = {
        name: "Test de création de notification",
        status: 'error',
        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      }
    }
    setResults([...tests])

    // Test 4: Test de ciblage 'tous les utilisateurs'
    try {
      const start = performance.now()
      const notificationData = {
        title: "Test ciblage global",
        body: "Test pour tous les utilisateurs",
        type: "system" as const,
        targetType: "all" as const,
        targetGroups: [],
        targetUsers: [],
        priority: "low" as const,
        createdBy: user.uid,
        status: "draft" as const
      }

      // Simuler la validation des données
      if (!notificationData.targetGroups || !notificationData.targetUsers) {
        throw new Error("Arrays targetGroups ou targetUsers undefined")
      }

      const duration = performance.now() - start
      tests[3] = {
        name: "Test de ciblage 'tous les utilisateurs'",
        status: 'success',
        message: "Validation du ciblage global réussie",
        duration
      }
    } catch (error) {
      tests[3] = {
        name: "Test de ciblage 'tous les utilisateurs'",
        status: 'error',
        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      }
    }
    setResults([...tests])

    // Test 5: Test de ciblage par groupes
    try {
      const start = performance.now()
      const notificationData = {
        title: "Test ciblage groupes",
        body: "Test pour groupes spécifiques",
        type: "system" as const,
        targetType: "groups" as const,
        targetGroups: ["admin", "editor"],
        targetUsers: [],
        priority: "normal" as const,
        createdBy: user.uid,
        status: "draft" as const
      }

      // Simuler la validation des données
      if (!notificationData.targetGroups || notificationData.targetGroups.length === 0) {
        throw new Error("targetGroups vide pour le ciblage par groupes")
      }

      const duration = performance.now() - start
      tests[4] = {
        name: "Test de ciblage par groupes",
        status: 'success',
        message: `Ciblage de ${notificationData.targetGroups.length} groupes validé`,
        duration
      }
    } catch (error) {
      tests[4] = {
        name: "Test de ciblage par groupes",
        status: 'error',
        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      }
    }
    setResults([...tests])

    // Test 6: Test des analytics
    try {
      const start = performance.now()
      const canUse = await NotificationAnalyticsService.canUseAnalytics(1)
      const summary = await NotificationAnalyticsService.getAnalyticsSummary(7)
      const duration = performance.now() - start

      tests[5] = {
        name: "Test des analytics",
        status: 'success',
        message: `Analytics ${canUse ? 'disponibles' : 'limitées'}, ${summary.totalSent} notifications dans les 7 derniers jours`,
        duration
      }
    } catch (error) {
      tests[5] = {
        name: "Test des analytics",
        status: 'error',
        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      }
    }
    setResults([...tests])

    // Test 7: Test de l'API d'envoi
    try {
      const start = performance.now()
      const response = await fetch('/api/notifications/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Important: inclure les cookies de session
        body: JSON.stringify({
          title: "Test API",
          body: "Test de l'API d'envoi de notifications",
          type: "system",
          targetType: "individual",
          targetUsers: [user.uid],
          priority: "low"
        })
      })

      const duration = performance.now() - start

      if (response.ok) {
        const result = await response.json()
        tests[6] = {
          name: "Test de l'API d'envoi",
          status: 'success',
          message: `API fonctionnelle, notification ID: ${result.notificationId}`,
          duration
        }
      } else {
        const error = await response.json()
        tests[6] = {
          name: "Test de l'API d'envoi",
          status: 'error',
          message: `Erreur API: ${error.error || 'Erreur inconnue'}`
        }
      }
    } catch (error) {
      tests[6] = {
        name: "Test de l'API d'envoi",
        status: 'error',
        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      }
    }
    setResults([...tests])

    // Test 8: Vérification des limites Firebase
    try {
      const start = performance.now()
      const settings = await NotificationAnalyticsService.getAnalyticsSettings()
      const usagePercentage = (settings.currentUsage / settings.dailyLimit) * 100
      const duration = performance.now() - start

      tests[7] = {
        name: "Vérification des limites Firebase",
        status: usagePercentage > 90 ? 'error' : usagePercentage > 70 ? 'error' : 'success',
        message: `Usage: ${settings.currentUsage}/${settings.dailyLimit} (${usagePercentage.toFixed(1)}%)`,
        duration
      }
    } catch (error) {
      tests[7] = {
        name: "Vérification des limites Firebase",
        status: 'error',
        message: `Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      }
    }
    setResults([...tests])

    setTesting(false)

    // Afficher un résumé
    const successCount = tests.filter(t => t.status === 'success').length
    const errorCount = tests.filter(t => t.status === 'error').length

    if (errorCount === 0) {
      toast.success(`Tous les tests réussis (${successCount}/${tests.length})`)
    } else {
      toast.error(`${errorCount} test(s) échoué(s) sur ${tests.length}`)
    }
  }

  // Obtenir l'icône de statut
  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'pending':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
    }
  }

  // Obtenir la couleur du badge
  const getBadgeVariant = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'default'
      case 'error':
        return 'destructive'
      case 'pending':
        return 'secondary'
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <TestTube className="h-5 w-5" />
                Tests du système de notifications
              </CardTitle>
              <CardDescription>
                Vérifiez le bon fonctionnement de tous les composants du système
              </CardDescription>
            </div>
            <Button
              onClick={runTests}
              disabled={testing}
            >
              {testing && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
              <TestTube className="h-4 w-4 mr-2" />
              Lancer les tests
            </Button>
          </div>
        </CardHeader>

        {results.length > 0 && (
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {results.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <p className="font-medium">{result.name}</p>
                      <p className="text-sm text-muted-foreground">{result.message}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {result.duration && (
                      <span className="text-xs text-muted-foreground">
                        {result.duration.toFixed(0)}ms
                      </span>
                    )}
                    <Badge variant={getBadgeVariant(result.status)}>
                      {result.status === 'pending' ? 'En cours' :
                       result.status === 'success' ? 'Réussi' : 'Échec'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>

            {!testing && results.length > 0 && (
              <div className="pt-4 border-t">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {results.filter(r => r.status === 'success').length}
                    </div>
                    <div className="text-sm text-muted-foreground">Réussis</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-red-600">
                      {results.filter(r => r.status === 'error').length}
                    </div>
                    <div className="text-sm text-muted-foreground">Échecs</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600">
                      {results.filter(r => r.duration).reduce((acc, r) => acc + (r.duration || 0), 0).toFixed(0)}ms
                    </div>
                    <div className="text-sm text-muted-foreground">Temps total</div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        )}
      </Card>
    </div>
  )
}
