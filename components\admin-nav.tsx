"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { auth } from "@/lib/firebase"
import {
  LayoutDashboard,
  Newspaper,
  FileText,
  Users,
  UserPlus,
  Upload,
  Settings,
  LogOut,
  Home,
  UserCheck,
  ClipboardList,
  Shield,
  Phone,
  Database,
  ActivityIcon as ActivityLog,
  WifiOff,
  Bell,
} from "lucide-react"
import { PermissionGate } from "./permission-gate"
import { PERMISSIONS } from "@/lib/permissions"

export function AdminNav() {
  const pathname = usePathname()

  const menuGroups = [
    {
      title: "Vue d'ensemble",
      items: [
        {
          title: "Tableau de bord",
          href: "/admin",
          icon: <LayoutDashboard className="h-4 w-4" />,
          permissions: [PERMISSIONS.ADMIN],
        },
      ],
    },
    {
      title: "Contenu",
      items: [
        {
          title: "Actualités",
          href: "/admin/news",
          icon: <Newspaper className="h-4 w-4" />,
          permissions: [
            PERMISSIONS.READ_NEWS,
            PERMISSIONS.CREATE_NEWS,
            PERMISSIONS.UPDATE_NEWS,
            PERMISSIONS.DELETE_NEWS,
            PERMISSIONS.ADMIN,
          ],
        },
        {
          title: "Pages",
          href: "/admin/pages",
          icon: <FileText className="h-4 w-4" />,
          permissions: [
            PERMISSIONS.READ_PAGES,
            PERMISSIONS.CREATE_PAGES,
            PERMISSIONS.UPDATE_PAGES,
            PERMISSIONS.DELETE_PAGES,
            PERMISSIONS.ADMIN,
          ],
        },
        {
          title: "Notifications",
          href: "/admin/notifications",
          icon: <Bell className="h-4 w-4" />,
          permissions: [PERMISSIONS.ADMIN],
        },
      ],
    },
    {
      title: "Gestion des utilisateurs",
      items: [
        {
          title: "Commerciaux",
          href: "/admin/commerciaux",
          icon: <Phone className="h-4 w-4" />,
          permissions: [PERMISSIONS.ADMIN, PERMISSIONS.READ_USERS, PERMISSIONS.UPDATE_USERS],
        },
        {
          title: "Utilisateurs",
          href: "/admin/users",
          icon: <Users className="h-4 w-4" />,
          permissions: [
            PERMISSIONS.READ_USERS,
            PERMISSIONS.CREATE_USERS,
            PERMISSIONS.UPDATE_USERS,
            PERMISSIONS.DELETE_USERS,
            PERMISSIONS.ADMIN,
          ],
        },
        {
          title: "En attente",
          href: "/admin/users/pending",
          icon: <UserCheck className="h-4 w-4" />,
          permissions: [PERMISSIONS.READ_USERS, PERMISSIONS.UPDATE_USERS, PERMISSIONS.ADMIN],
        },
        {
          title: "Pré-enregistrés",
          href: "/admin/pre-registered",
          icon: <ClipboardList className="h-4 w-4" />,
          permissions: [PERMISSIONS.READ_USERS, PERMISSIONS.CREATE_USERS, PERMISSIONS.ADMIN],
        },
        {
          title: "Groupes",
          href: "/admin/groups",
          icon: <UserPlus className="h-4 w-4" />,
          permissions: [
            PERMISSIONS.READ_GROUPS,
            PERMISSIONS.CREATE_GROUPS,
            PERMISSIONS.UPDATE_GROUPS,
            PERMISSIONS.DELETE_GROUPS,
            PERMISSIONS.ADMIN,
          ],
        },
        {
          title: "Rôles",
          href: "/admin/roles",
          icon: <Shield className="h-4 w-4" />,
          permissions: [PERMISSIONS.ADMIN],
        },
      ],
    },
    {
      title: "Outils & Configuration",
      items: [
        {
          title: "Import CSV",
          href: "/admin/import",
          icon: <Upload className="h-4 w-4" />,
          permissions: [PERMISSIONS.MANAGE_IMPORT, PERMISSIONS.ADMIN],
        },
        {
          title: "Paramètres",
          href: "/admin/settings",
          icon: <Settings className="h-4 w-4" />,
          permissions: [PERMISSIONS.READ_SETTINGS, PERMISSIONS.UPDATE_SETTINGS, PERMISSIONS.ADMIN],
        },
        {
          title: "Diagnostic",
          href: "/admin/diagnostics",
          icon: <ActivityLog className="h-4 w-4" />,
          permissions: [PERMISSIONS.ADMIN],
        },
        {
          title: "Cache",
          href: "/admin/cache",
          icon: <Database className="h-4 w-4" />,
          permissions: [PERMISSIONS.ADMIN],
        },
        {
          title: "Mode hors ligne",
          href: "/admin/offline",
          icon: <WifiOff className="h-4 w-4" />,
          permissions: [PERMISSIONS.ADMIN],
        },
      ],
    },
  ]

  return (
    <nav className="flex flex-col gap-1 py-2">
      <Link href="/dashboard">
        <Button
          variant="outline"
          className="w-full justify-start mb-4 border-primary/20 hover:bg-primary/5 hover:text-primary text-sm dark:border-primary/30 h-10 transition-all duration-200"
          size="sm"
        >
          <Home className="h-4 w-4 mr-3" />
          Retour à l'accueil
        </Button>
      </Link>

      {menuGroups.map((group, groupIndex) => (
        <div key={groupIndex} className="mb-4">
          <h3 className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            {group.title}
          </h3>
          <div className="space-y-1">
            {group.items.map((item, itemIndex) => (
              <PermissionGate key={itemIndex} permissions={item.permissions} anyPermission={true}>
                <Link href={item.href}>
                  <Button
                    variant={pathname === item.href ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start text-sm h-10 transition-all duration-200",
                      pathname === item.href
                        ? "bg-primary/10 text-primary font-medium hover:bg-primary/15 dark:bg-primary/20 dark:text-white"
                        : "hover:bg-muted/50 hover:text-foreground",
                    )}
                    size="sm"
                  >
                    <div className="flex items-center gap-3 min-w-0">
                      <div className="flex-shrink-0">{item.icon}</div>
                      <span className="truncate font-medium">{item.title}</span>
                    </div>
                  </Button>
                </Link>
              </PermissionGate>
            ))}
          </div>
        </div>
      ))}

      <div className="border-t pt-4 mt-2">
        <Button
          variant="ghost"
          className="w-full justify-start text-red-500 hover:bg-red-50 hover:text-red-600 dark:text-red-400 dark:hover:bg-red-950/20 dark:hover:text-red-300 text-sm h-10 transition-all duration-200"
          onClick={() => auth.signOut()}
          size="sm"
        >
          <LogOut className="h-4 w-4 mr-3" />
          Déconnexion
        </Button>
      </div>
    </nav>
  )
}
