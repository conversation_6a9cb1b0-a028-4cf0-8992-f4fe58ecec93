/**
 * <PERSON>ript pour initialiser le document notificationAnalytics manquant
 * À exécuter une seule fois pour corriger l'erreur Firebase
 */

import { initializeApp } from 'firebase/app'
import { getFirestore, doc, setDoc, serverTimestamp } from 'firebase/firestore'

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
}

async function initializeNotificationAnalytics() {
  try {
    console.log('Initialisation de Firebase...')
    const app = initializeApp(firebaseConfig)
    const db = getFirestore(app)

    console.log('Création du document notificationAnalytics...')
    const settingsRef = doc(db, "settings", "notificationAnalytics")
    
    const defaultSettings = {
      enabled: true,
      dailyLimit: 45000, // 90% de la limite Firebase (50k)
      currentUsage: 0,
      lastReset: new Date().toISOString().split('T')[0],
      autoDisableOnLimit: true,
      priorityMode: 'normal',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }

    await setDoc(settingsRef, defaultSettings)
    
    console.log('✅ Document notificationAnalytics créé avec succès!')
    console.log('Les erreurs Firebase Analytics devraient maintenant être résolues.')
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error)
  }
}

// Exécuter le script si appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeNotificationAnalytics()
}

export { initializeNotificationAnalytics }
