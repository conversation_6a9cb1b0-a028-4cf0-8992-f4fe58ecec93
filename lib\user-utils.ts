import { doc, getDoc, collection, getDocs, query, where } from "firebase/firestore"
import { db } from "@/lib/firebase"
import type { Permission } from "@/lib/permissions"
import { VersionService } from "./version-service"

// Cache global pour éviter les appels redondants
const groupsCache = new Map<string, { groups: string[], timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Améliorer la fonction getUserGroups pour être plus robuste
export async function getUserGroups(userId: string): Promise<string[]> {
  try {
    console.log(`Récupération des groupes pour l'utilisateur: ${userId}`)

    // Vérifier le cache en mémoire d'abord
    const memoryCache = groupsCache.get(userId)
    if (memoryCache && (Date.now() - memoryCache.timestamp) < CACHE_DURATION) {
      console.log(`Utilisation des groupes en cache mémoire pour l'utilisateur: ${userId}`)
      return memoryCache.groups
    }

    // Vérifier d'abord le cache localStorage
    const cachedGroups = localStorage.getItem(`user_groups_${userId}`)
    const cachedTimestamp = localStorage.getItem(`user_groups_timestamp_${userId}`)
    const lastPermissionUpdate = localStorage.getItem(`user_permission_update_${userId}`)

    // Utiliser le cache si disponible et qu'il n'y a pas eu de mise à jour des permissions
    if (cachedGroups && cachedTimestamp && lastPermissionUpdate) {
      const cacheTime = Number.parseInt(cachedTimestamp)
      const updateTime = Number.parseInt(lastPermissionUpdate)

      // Si le cache est plus récent que la dernière mise à jour des permissions, l'utiliser
      if (cacheTime > updateTime) {
        const groups = JSON.parse(cachedGroups)
        console.log(`Utilisation des groupes en cache pour l'utilisateur: ${userId}`)

        // Mettre à jour le cache mémoire
        groupsCache.set(userId, { groups, timestamp: Date.now() })

        return groups
      }
    }

    // Sinon, charger depuis Firestore
    console.log(`Chargement des groupes depuis Firestore pour l'utilisateur: ${userId}`)
    const userDoc = await getDoc(doc(db(), "users", userId))

    if (userDoc.exists()) {
      const userData = userDoc.data()
      const groups = userData.groups || []
      const permissionUpdateTime = userData.lastPermissionUpdate || Date.now()

      console.log(`Groupes chargés depuis Firestore: ${groups.join(", ")}`)

      // Mettre en cache les groupes et le timestamp de mise à jour des permissions
      localStorage.setItem(`user_groups_${userId}`, JSON.stringify(groups))
      localStorage.setItem(`user_groups_timestamp_${userId}`, Date.now().toString())
      localStorage.setItem(`user_permission_update_${userId}`, permissionUpdateTime.toString())

      // Mettre à jour le cache mémoire
      groupsCache.set(userId, { groups, timestamp: Date.now() })

      return groups
    } else {
      console.warn(`Document utilisateur pour ${userId} introuvable dans Firestore`)

      // Vérifier si nous avons un cache périmé à utiliser comme solution de secours
      if (cachedGroups) {
        console.log(`Utilisation du cache périmé comme solution de secours pour l'utilisateur: ${userId}`)
        return JSON.parse(cachedGroups)
      }

      return []
    }
  } catch (error) {
    console.error("Erreur lors de la récupération des groupes utilisateur:", error)

    // En cas d'erreur, essayer d'utiliser le cache même s'il est périmé
    const cachedGroups = localStorage.getItem(`user_groups_${userId}`)
    if (cachedGroups) {
      console.log("Utilisation du cache comme solution de secours après erreur")
      return JSON.parse(cachedGroups)
    }

    return []
  }
}

// Améliorer la fonction getUserMenuItems pour être plus robuste
export async function getUserMenuItems(userId: string, userPermissions: Permission[]) {
  try {
    console.log(`Récupération des éléments de menu pour l'utilisateur: ${userId}`)
    console.log("getUserMenuItems called with userPermissions:", userPermissions)

    // Vérifier d'abord le cache
    const cachedMenuItems = localStorage.getItem(`menu_items_${userId}`)
    const cachedMenuVersion = localStorage.getItem(`menu_version_${userId}`)

    // Si nous avons un cache, l'utiliser immédiatement pour éviter les délais
    if (cachedMenuItems) {
      const parsedItems = JSON.parse(cachedMenuItems)
      console.log(`Utilisation des ${parsedItems.length} éléments de menu en cache`)

      // Continuer le processus en arrière-plan pour mettre à jour le cache si nécessaire
      setTimeout(() => updateMenuItemsCache(userId, userPermissions, cachedMenuVersion), 0)

      return parsedItems
    }

    // Récupérer les groupes de l'utilisateur
    const userGroups = await getUserGroups(userId)
    console.log(`Groupes de l'utilisateur pour le menu: ${userGroups.join(", ")}`)

    // Si l'utilisateur n'a pas de groupes, retourner un menu par défaut
    if (!userGroups || userGroups.length === 0) {
      console.log("Aucun groupe trouvé, utilisation du menu par défaut")
      const defaultItems = getDefaultMenuItems()

      // Mettre en cache le menu par défaut
      localStorage.setItem(`menu_items_${userId}`, JSON.stringify(defaultItems))

      return defaultItems
    }

    // Si pas de cache, charger depuis Firestore
    try {
      console.log("Chargement des éléments de menu depuis Firestore")
      return await loadMenuItemsFromFirestore(userId, userPermissions, userGroups)
    } catch (error) {
      console.error("Erreur lors du chargement des éléments de menu:", error)

      // En cas d'erreur, retourner un menu par défaut
      const defaultItems = getDefaultMenuItems()

      // Mettre en cache le menu par défaut
      localStorage.setItem(`menu_items_${userId}`, JSON.stringify(defaultItems))

      return defaultItems
    }
  } catch (error) {
    console.error("Erreur lors de la récupération des éléments de menu:", error)

    // En cas d'erreur, essayer d'utiliser le cache même s'il est périmé
    const cachedMenuItems = localStorage.getItem(`menu_items_${userId}`)
    if (cachedMenuItems) {
      return JSON.parse(cachedMenuItems)
    }

    // Si pas de cache, retourner un menu par défaut
    return getDefaultMenuItems()
  }
}

// Modifier la fonction loadMenuItemsFromFirestore pour prendre en compte les groupes
async function loadMenuItemsFromFirestore(userId: string, userPermissions: Permission[], userGroups: string[] = []) {
  try {
    console.log(`Chargement des éléments de menu depuis Firestore pour les groupes: ${userGroups.join(", ")}`)

    // Si l'utilisateur n'a pas de groupes, retourner un tableau vide
    if (!userGroups.length) {
      console.log("Aucun groupe trouvé, retour d'un menu vide")
      return getDefaultMenuItems()
    }

    // Récupérer tous les éléments de menu publiés
    const menuItemsRef = collection(db(), "menuItems")
    const menuItemsQuery = query(menuItemsRef, where("isPublished", "==", true))
    const menuItemsSnapshot = await getDocs(menuItemsQuery)

    const items: any[] = []

    menuItemsSnapshot.forEach((doc) => {
      const data = doc.data()

      // Vérifier si cet élément de menu est pour l'un des groupes de l'utilisateur ou pour tous les groupes
      const isForUser =
        data.targetGroups?.includes("all") ||
        (Array.isArray(data.targetGroups) && data.targetGroups.some((group: string) => userGroups.includes(group)))

      // Vérifier si l'utilisateur a les autorisations requises pour voir la page
      const hasRequiredPermissions = true // Temporairement permettre à tous les utilisateurs

      if (isForUser && data.isPublished && hasRequiredPermissions) {
        items.push({
          id: doc.id,
          title: data.title,
          path: `/dashboard/pages/${data.slug}`,
          iconUrl: data.iconUrl || null,
          displayOrder: data.displayOrder !== undefined ? data.displayOrder : 0,
        })
      }
    })

    // Trier les éléments par ordre d'affichage
    const sortedItems = items.sort((a, b) => {
      const orderA = a.displayOrder !== undefined ? a.displayOrder : 0
      const orderB = b.displayOrder !== undefined ? b.displayOrder : 0
      return orderA - orderB
    })

    console.log(`${sortedItems.length} éléments de menu chargés et triés`)

    // Mettre en cache les éléments de menu
    localStorage.setItem(`menu_items_${userId}`, JSON.stringify(sortedItems))
    localStorage.setItem(`menu_version_${userId}`, "1")

    return sortedItems
  } catch (error) {
    console.error("Erreur lors du chargement des éléments de menu depuis Firestore:", error)

    // En cas d'erreur, retourner un menu par défaut
    return getDefaultMenuItems()
  }
}

// Fonction pour mettre à jour le cache des éléments de menu en arrière-plan
async function updateMenuItemsCache(userId: string, userPermissions: Permission[], cachedMenuVersion: string | null) {
  try {
    // Récupérer la version actuelle du menu
    let currentMenuVersion = 1
    try {
      currentMenuVersion = await VersionService.getMenuVersion()
    } catch (error) {
      console.warn("Could not get menu version, using default:", error)
      // Continuer avec la version par défaut
    }

    // Si la version du cache correspond à la version actuelle, pas besoin de mettre à jour
    if (cachedMenuVersion && Number.parseInt(cachedMenuVersion) === currentMenuVersion) {
      return
    }

    // Récupérer les groupes de l'utilisateur
    const userGroups = await getUserGroups(userId)

    // Charger depuis Firestore et mettre à jour le cache
    const items = await loadMenuItemsFromFirestore(userId, userPermissions, userGroups)

    // Mettre en cache les éléments de menu avec la version actuelle
    localStorage.setItem(`menu_items_${userId}`, JSON.stringify(items))
    localStorage.setItem(`menu_version_${userId}`, currentMenuVersion.toString())

    console.log("Menu items cache updated in background")
  } catch (error) {
    console.error("Error updating menu items cache:", error)
  }
}

// Fonction pour obtenir un menu par défaut en cas d'erreur
function getDefaultMenuItems() {
  return [
    {
      id: "default-home",
      title: "Accueil",
      path: "/dashboard",
      iconUrl: null,
      displayOrder: 0,
    },
    {
      id: "default-news",
      title: "Actualités",
      path: "/dashboard/news",
      iconUrl: null,
      displayOrder: 1,
    },
  ]
}
