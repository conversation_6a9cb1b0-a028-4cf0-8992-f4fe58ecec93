"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { 
  Settings, 
  Bell, 
  Clock, 
  Shield, 
  Zap,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Globe
} from "lucide-react"
import { doc, getDoc, setDoc, serverTimestamp } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { toast } from "sonner"

interface NotificationSettings {
  // Paramètres généraux
  pushEnabled: boolean
  inAppEnabled: boolean
  emailEnabled: boolean
  
  // Limites et quotas
  dailyLimitPerUser: number
  globalDailyLimit: number
  rateLimitWindow: number // en minutes
  
  // Heures silencieuses globales
  quietHoursEnabled: boolean
  quietHoursStart: string
  quietHoursEnd: string
  
  // Paramètres de sécurité
  requireAuth: boolean
  adminOnlyCreate: boolean
  logAllActions: boolean
  
  // Paramètres de performance
  batchSize: number
  retryAttempts: number
  retryDelay: number // en secondes
  
  // Templates par défaut
  defaultTitle: string
  defaultBody: string
  
  // Paramètres avancés
  enableAnalytics: boolean
  enableScheduling: boolean
  enableGroupTargeting: boolean
  
  // Métadonnées
  updatedAt?: any
  updatedBy?: string
}

interface NotificationSettingsProps {
  className?: string
}

export function NotificationSettingsComponent({ className }: NotificationSettingsProps) {
  const [settings, setSettings] = useState<NotificationSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // États pour les formulaires
  const [formData, setFormData] = useState<NotificationSettings>({
    pushEnabled: true,
    inAppEnabled: true,
    emailEnabled: false,
    dailyLimitPerUser: 10,
    globalDailyLimit: 1000,
    rateLimitWindow: 60,
    quietHoursEnabled: true,
    quietHoursStart: "22:00",
    quietHoursEnd: "08:00",
    requireAuth: true,
    adminOnlyCreate: true,
    logAllActions: true,
    batchSize: 100,
    retryAttempts: 3,
    retryDelay: 30,
    defaultTitle: "",
    defaultBody: "",
    enableAnalytics: true,
    enableScheduling: true,
    enableGroupTargeting: true
  })

  // Charger les paramètres
  const loadSettings = async () => {
    try {
      setLoading(true)
      const settingsRef = doc(db(), "settings", "notifications")
      const snapshot = await getDoc(settingsRef)
      
      if (snapshot.exists()) {
        const data = snapshot.data() as NotificationSettings
        setSettings(data)
        setFormData(data)
      } else {
        // Utiliser les paramètres par défaut
        setSettings(formData)
      }
    } catch (error) {
      console.error("Erreur lors du chargement des paramètres:", error)
      toast.error("Impossible de charger les paramètres")
    } finally {
      setLoading(false)
    }
  }

  // Sauvegarder les paramètres
  const saveSettings = async () => {
    try {
      setSaving(true)
      
      const settingsToSave = {
        ...formData,
        updatedAt: serverTimestamp(),
        updatedBy: "admin" // À remplacer par l'ID de l'utilisateur connecté
      }

      const settingsRef = doc(db(), "settings", "notifications")
      await setDoc(settingsRef, settingsToSave)
      
      setSettings(settingsToSave)
      setHasChanges(false)
      toast.success("Paramètres sauvegardés avec succès")
    } catch (error) {
      console.error("Erreur lors de la sauvegarde:", error)
      toast.error("Impossible de sauvegarder les paramètres")
    } finally {
      setSaving(false)
    }
  }

  // Gérer les changements de formulaire
  const handleChange = (field: keyof NotificationSettings, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    setHasChanges(true)
  }

  // Réinitialiser aux valeurs par défaut
  const resetToDefaults = () => {
    const defaultSettings: NotificationSettings = {
      pushEnabled: true,
      inAppEnabled: true,
      emailEnabled: false,
      dailyLimitPerUser: 10,
      globalDailyLimit: 1000,
      rateLimitWindow: 60,
      quietHoursEnabled: true,
      quietHoursStart: "22:00",
      quietHoursEnd: "08:00",
      requireAuth: true,
      adminOnlyCreate: true,
      logAllActions: true,
      batchSize: 100,
      retryAttempts: 3,
      retryDelay: 30,
      defaultTitle: "",
      defaultBody: "",
      enableAnalytics: true,
      enableScheduling: true,
      enableGroupTargeting: true
    }
    setFormData(defaultSettings)
    setHasChanges(true)
  }

  useEffect(() => {
    loadSettings()
  }, [])

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* En-tête avec actions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="text-lg">Configuration des notifications</CardTitle>
            <CardDescription>
              Paramètres globaux pour le système de notifications
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={resetToDefaults}
              disabled={saving}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Réinitialiser
            </Button>
            <Button
              onClick={saveSettings}
              disabled={saving || !hasChanges}
            >
              {saving && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
              <Save className="h-4 w-4 mr-2" />
              Sauvegarder
            </Button>
          </div>
        </CardHeader>
        {hasChanges && (
          <CardContent>
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Vous avez des modifications non sauvegardées.
              </AlertDescription>
            </Alert>
          </CardContent>
        )}
      </Card>

      {/* Paramètres généraux */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Paramètres généraux
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="push-enabled">Notifications push</Label>
                <p className="text-sm text-muted-foreground">
                  Activer les notifications push
                </p>
              </div>
              <Switch
                id="push-enabled"
                checked={formData.pushEnabled}
                onCheckedChange={(value) => handleChange('pushEnabled', value)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="inapp-enabled">Notifications in-app</Label>
                <p className="text-sm text-muted-foreground">
                  Activer les notifications dans l'application
                </p>
              </div>
              <Switch
                id="inapp-enabled"
                checked={formData.inAppEnabled}
                onCheckedChange={(value) => handleChange('inAppEnabled', value)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="email-enabled">Notifications email</Label>
                <p className="text-sm text-muted-foreground">
                  Activer les notifications par email
                </p>
              </div>
              <Switch
                id="email-enabled"
                checked={formData.emailEnabled}
                onCheckedChange={(value) => handleChange('emailEnabled', value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Limites et quotas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Limites et quotas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="daily-limit-user">Limite quotidienne par utilisateur</Label>
              <Input
                id="daily-limit-user"
                type="number"
                value={formData.dailyLimitPerUser}
                onChange={(e) => handleChange('dailyLimitPerUser', parseInt(e.target.value))}
                min="1"
                max="100"
              />
              <p className="text-sm text-muted-foreground">
                Nombre maximum de notifications par utilisateur par jour
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="global-daily-limit">Limite quotidienne globale</Label>
              <Input
                id="global-daily-limit"
                type="number"
                value={formData.globalDailyLimit}
                onChange={(e) => handleChange('globalDailyLimit', parseInt(e.target.value))}
                min="100"
                max="10000"
              />
              <p className="text-sm text-muted-foreground">
                Nombre maximum de notifications envoyées par jour
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="rate-limit">Fenêtre de limitation (minutes)</Label>
              <Input
                id="rate-limit"
                type="number"
                value={formData.rateLimitWindow}
                onChange={(e) => handleChange('rateLimitWindow', parseInt(e.target.value))}
                min="1"
                max="1440"
              />
              <p className="text-sm text-muted-foreground">
                Durée de la fenêtre de limitation de débit
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Heures silencieuses */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Heures silencieuses
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="quiet-hours">Activer les heures silencieuses</Label>
              <p className="text-sm text-muted-foreground">
                Période pendant laquelle les notifications ne sont pas envoyées
              </p>
            </div>
            <Switch
              id="quiet-hours"
              checked={formData.quietHoursEnabled}
              onCheckedChange={(value) => handleChange('quietHoursEnabled', value)}
            />
          </div>

          {formData.quietHoursEnabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quiet-start">Heure de début</Label>
                <Input
                  id="quiet-start"
                  type="time"
                  value={formData.quietHoursStart}
                  onChange={(e) => handleChange('quietHoursStart', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="quiet-end">Heure de fin</Label>
                <Input
                  id="quiet-end"
                  type="time"
                  value={formData.quietHoursEnd}
                  onChange={(e) => handleChange('quietHoursEnd', e.target.value)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Paramètres avancés */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Paramètres avancés
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enable-analytics">Analytics</Label>
                <p className="text-sm text-muted-foreground">
                  Activer le suivi des performances
                </p>
              </div>
              <Switch
                id="enable-analytics"
                checked={formData.enableAnalytics}
                onCheckedChange={(value) => handleChange('enableAnalytics', value)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enable-scheduling">Programmation</Label>
                <p className="text-sm text-muted-foreground">
                  Activer les notifications programmées
                </p>
              </div>
              <Switch
                id="enable-scheduling"
                checked={formData.enableScheduling}
                onCheckedChange={(value) => handleChange('enableScheduling', value)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enable-groups">Ciblage par groupes</Label>
                <p className="text-sm text-muted-foreground">
                  Activer le ciblage par groupes d'utilisateurs
                </p>
              </div>
              <Switch
                id="enable-groups"
                checked={formData.enableGroupTargeting}
                onCheckedChange={(value) => handleChange('enableGroupTargeting', value)}
              />
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="batch-size">Taille des lots</Label>
              <Input
                id="batch-size"
                type="number"
                value={formData.batchSize}
                onChange={(e) => handleChange('batchSize', parseInt(e.target.value))}
                min="10"
                max="1000"
              />
              <p className="text-sm text-muted-foreground">
                Nombre de notifications traitées par lot
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="retry-attempts">Tentatives de retry</Label>
              <Input
                id="retry-attempts"
                type="number"
                value={formData.retryAttempts}
                onChange={(e) => handleChange('retryAttempts', parseInt(e.target.value))}
                min="0"
                max="10"
              />
              <p className="text-sm text-muted-foreground">
                Nombre de tentatives en cas d'échec
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="retry-delay">Délai de retry (secondes)</Label>
              <Input
                id="retry-delay"
                type="number"
                value={formData.retryDelay}
                onChange={(e) => handleChange('retryDelay', parseInt(e.target.value))}
                min="1"
                max="300"
              />
              <p className="text-sm text-muted-foreground">
                Délai d'attente entre les tentatives
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
