{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run generate-sw && next dev", "build": "npm run generate-sw && next build", "start": "next start", "lint": "next lint", "generate-sw": "node scripts/generate-sw.js"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@hello-pangea/dnd": "latest", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tiptap/core": "latest", "@tiptap/extension-hard-break": "latest", "@tiptap/extension-image": "latest", "@tiptap/extension-link": "latest", "@tiptap/extension-placeholder": "latest", "@tiptap/extension-text-align": "latest", "@tiptap/pm": "latest", "@tiptap/react": "latest", "@tiptap/starter-kit": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "diff": "latest", "embla-carousel-react": "8.5.1", "firebase": "latest", "firebase-admin": "latest", "framer-motion": "latest", "fs": "latest", "input-otp": "1.4.1", "jose": "latest", "lighthouse": "latest", "localforage": "latest", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "papaparse": "latest", "path": "latest", "prop-types": "latest", "puppeteer": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "react-simple-maps": "latest", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "url": "latest", "vaul": "^0.9.6", "workbox-window": "latest", "zod": "latest"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}